import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Check, ChevronDown, FileText, Plus, Search, X } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { cn } from '@/lib/utils';

interface Scheme {
  id: string;
  name: string;
  scheme_code: string;
  interest_rate: number;
  interest_type: string;
  tenure_months: number;
  min_amount: number;
  max_amount: number;
  lock_in_period_months: number;
  commission_percentage: number;
  compounding_frequency: string;
  payout_type: string;
  supports_sip: boolean;
}

interface SchemeSelectorProps {
  selectedScheme: Scheme | null;
  onSchemeSelect: (scheme: Scheme | null) => void;
  onAddScheme?: () => void;
}

const SchemeSelector: React.FC<SchemeSelectorProps> = ({
  selectedScheme,
  onSchemeSelect,
  onAddScheme
}) => {
  const [schemes, setSchemes] = useState<Scheme[]>([]);
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchSchemes();
  }, []);

  const fetchSchemes = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('schemes')
        .select('*')
        .eq('is_active', true)
        .order('name');
      if (error) throw error;
      
      // Ensure all schemes have valid data
      const validSchemes = (data || []).map(scheme => ({
        id: scheme.id,
        name: scheme.name || '',
        scheme_code: scheme.scheme_code || '',
        interest_rate: scheme.interest_rate || 0,
        interest_type: scheme.interest_type || '',
        tenure_months: scheme.tenure_months || 0,
        min_amount: scheme.min_amount || 0,
        max_amount: scheme.max_amount || 0,
        lock_in_period_months: scheme.lock_in_period_months || 0,
        commission_percentage: scheme.commission_percentage || 0,
        compounding_frequency: scheme.compounding_frequency || '',
        payout_type: scheme.payout_type || '',
        supports_sip: scheme.supports_sip || false
      }));
      setSchemes(validSchemes);
    } catch (err) {
      console.error('Error fetching schemes:', err);
      setSchemes([]);
    } finally {
      setLoading(false);
    }
  };

  const handleSchemeSelect = (scheme: Scheme) => {
    onSchemeSelect(scheme);
    setOpen(false);
    setSearchTerm('');
  };

  const handleClear = () => {
    onSchemeSelect(null);
    setSearchTerm('');
  };

  // Helper function to get display name
  const getSchemeDisplayName = (scheme: Scheme) => {
    return `${scheme.name} (${scheme.scheme_code})`;
  };

  // Filter schemes based on search term
  const filteredSchemes = schemes.filter(scheme => {
    const searchLower = searchTerm.toLowerCase();
    const name = (scheme.name || '').toLowerCase();
    const code = (scheme.scheme_code || '').toLowerCase();
    
    return name.includes(searchLower) || code.includes(searchLower);
  });

  return (
    <div className="relative w-full">
      <Button
        type="button"
        variant="outline"
        role="combobox"
        aria-expanded={open}
        onClick={() => setOpen(!open)}
        className="w-full justify-between h-10 bg-white border border-gray-300 hover:bg-gray-50 text-left"
      >
        {selectedScheme ? (
          <span className="text-gray-900 truncate">
            {getSchemeDisplayName(selectedScheme)}
          </span>
        ) : (
          <span className="text-gray-500">Select a scheme...</span>
        )}
        <div className="flex items-center gap-1">
          {selectedScheme && (
            <button
              type="button"
              onClick={(e) => {
                e.stopPropagation();
                handleClear();
              }}
              className="p-1 hover:bg-gray-200 rounded"
            >
              <X className="h-3 w-3 text-gray-400" />
            </button>
          )}
          <ChevronDown className="h-4 w-4 text-gray-400" />
        </div>
      </Button>

      {open && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-96 overflow-hidden">
          {/* Search Input */}
          <div className="p-2 border-b">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search schemes..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 h-9 text-sm"
                autoFocus
              />
            </div>
          </div>

          {/* Scheme List */}
          <div className="max-h-80 overflow-y-auto">
            {loading ? (
              <div className="p-4 text-center text-sm text-gray-500">
                Loading schemes...
              </div>
            ) : filteredSchemes.length === 0 ? (
              <div className="p-4 text-center text-sm text-gray-500">
                {searchTerm ? 'No schemes found.' : 'No schemes available.'}
              </div>
            ) : (
              filteredSchemes.map((scheme) => (
                <button
                  key={scheme.id}
                  type="button"
                  onClick={() => handleSchemeSelect(scheme)}
                  className={cn(
                    "w-full flex items-center gap-3 px-3 py-3 text-left hover:bg-gray-50 transition-colors",
                    selectedScheme?.id === scheme.id && "bg-green-50"
                  )}
                >
                  <div className="flex items-center justify-center w-8 h-8 rounded-full bg-green-100">
                    <FileText className="h-4 w-4 text-green-600" />
                  </div>
                  <div className="flex flex-col flex-1 min-w-0">
                    <span className="font-medium text-gray-900 text-sm truncate">
                      {scheme.name}
                    </span>
                    <span className="text-sm text-gray-500 truncate">
                      {scheme.scheme_code} • {scheme.interest_rate}% • {scheme.tenure_months} months
                    </span>
                  </div>
                  {selectedScheme?.id === scheme.id && (
                    <Check className="h-4 w-4 text-green-600 shrink-0" />
                  )}
                </button>
              ))
            )}
          </div>

          {/* Add New Scheme Button */}
          <div className="border-t p-1">
            <button
              type="button"
              onClick={() => {
                setOpen(false);
                if (onAddScheme) onAddScheme();
                else alert('Implement Add New Scheme action');
              }}
              className="flex items-center gap-2 w-full px-3 py-3 text-sm text-green-600 hover:bg-green-50 rounded-sm"
            >
              <Plus className="h-4 w-4" />
              Add New Scheme
            </button>
          </div>
        </div>
      )}

      {/* Click outside to close */}
      {open && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setOpen(false)}
        />
      )}
    </div>
  );
};

export default SchemeSelector;