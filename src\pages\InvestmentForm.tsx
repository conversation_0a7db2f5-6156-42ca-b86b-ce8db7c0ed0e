
import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { EnhancedDatePicker } from '@/components/ui/enhanced-date-picker';
import { Textarea } from '@/components/ui/textarea';
import { Plus, ArrowLeft, ArrowRight } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import ClientSelector from '@/components/investment/ClientSelector';
import SchemeSelector from '@/components/investment/SchemeSelector';
import NomineeSelector from '@/components/investment/NomineeSelector';

interface Client {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
}

interface Scheme {
  id: string;
  name: string;
  scheme_code: string;
  interest_rate: number;
  interest_type: string;
  tenure_months: number;
  min_amount: number;
  max_amount: number;
  lock_in_period_months: number;
  commission_percentage: number;
  compounding_frequency: string;
  payout_type: string;
  supports_sip: boolean;
}

interface Nominee {
  id: string;
  name: string;
  relation: string;
}
interface OriginalInvestment {
  id: string;
  maturity_amount: number;
  scheme_name: string;
}
interface SbAccount {
  id: string;
  sb_account_number: string;
  account_type: string; // changed from: 'single' | 'joint'
  status: string;        // changed from: 'active' | 'inactive'
  client_sb_accounts: {
    role: string; // optional: narrow to 'primary' | 'secondary' if strict
    client_id: string;
    clients: {
      id: string;
      first_name: string;
      last_name: string;
    };
  }[];
}

interface PrefilledInvestmentState {
  prefilledData?: {
    client_id?: string;
    amount?: number;
    remark?: string;
    isReinvestment?: boolean;
    reinvestment_source_id?: string;
    originalInvestment?: OriginalInvestment;
  };
}


const InvestmentForm: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [tdsPercentage, setTdsPercentage] = useState(10);
  const [isReinvestment, setIsReinvestment] = useState(false);
  const [reinvestmentSourceId, setReinvestmentSourceId] = useState<string | null>(null);
  const [originalInvestment, setOriginalInvestment] = useState<OriginalInvestment | null>(null);
  const [sbAccounts, setSbAccounts] = useState<SbAccount[]>([]);

  const [showNewAccountForm, setShowNewAccountForm] = useState(false);

  // Investment Form state
  const [amountError, setAmountError] = useState<string | null>(null);
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [selectedSecondaryApplicant, setSelectedSecondaryApplicant] = useState<Client | null>(null);
  const [selectedScheme, setSelectedScheme] = useState<Scheme | null>(null);
  const [selectedNominee, setSelectedNominee] = useState<Nominee | null>(null);
  const [amount, setAmount] = useState<string>('');
  const [investmentDate, setInvestmentDate] = useState<Date>();
  const [startDate, setStartDate] = useState<Date>();
  const [maturityDate, setMaturityDate] = useState<Date>();
  const [maturityAmount, setMaturityAmount] = useState<number>(0);
  const [remark, setRemark] = useState('');
  const [commission, setCommission] = useState<number>(0);
  const [tdsAmount, setTdsAmount] = useState<number>(0);
  const [actualProfit, setActualProfit] = useState<number>(0);

  // Transaction Form state
  const [paymentMode, setPaymentMode] = useState('');
  const [selectedSbAccount, setSelectedSbAccount] = useState('');
  const [referenceNumber, setReferenceNumber] = useState('');
  const [newAccountNumber, setNewAccountNumber] = useState('');

  useEffect(() => {
    fetchTdsPercentage();

    // Handle pre-filled data from reinvestment
    const state = location.state as PrefilledInvestmentState;

    if (state?.prefilledData) {
      const { prefilledData } = state;
      if (prefilledData.client_id) {
        fetchClientById(prefilledData.client_id);
      }
      if (prefilledData.amount) setAmount(prefilledData.amount.toString());
      if (prefilledData.remark) setRemark(prefilledData.remark);
      if (prefilledData.isReinvestment) setIsReinvestment(true);
      if (prefilledData.reinvestment_source_id) setReinvestmentSourceId(prefilledData.reinvestment_source_id);
      if (prefilledData.originalInvestment) setOriginalInvestment(prefilledData.originalInvestment);
    }
  }, [location.state]);

  // Fetch SB accounts when clients change
  useEffect(() => {
    if (selectedClient) {
      fetchSbAccounts();
    }
  }, [selectedClient, selectedSecondaryApplicant]);

  useEffect(() => {
    if (selectedScheme && amount && investmentDate) {
      calculateMaturityDetails();
    }
  }, [selectedScheme, amount, investmentDate]);

  const fetchClientById = async (clientId: string) => {
    try {
      const { data, error } = await supabase
        .from('clients')
        .select('id, first_name, last_name, email')
        .eq('id', clientId)
        .single();

      if (error) throw error;
      setSelectedClient(data);
    } catch (error) {
      console.error('Error fetching client:', error);
    }
  };

  const fetchTdsPercentage = async () => {
    try {
      const { data, error } = await supabase
        .from('notification_settings')
        .select('tds_percentage')
        .limit(1)
        .single();

      if (error) throw error;
      if (data?.tds_percentage) {
        setTdsPercentage(data.tds_percentage);
      }
    } catch (error) {
      console.error('Error fetching TDS percentage:', error);
    }
  };

  const fetchSbAccounts = async () => {
    if (!selectedClient) {
      setSbAccounts([]);
      return;
    }

    try {
      // Get client IDs to filter accounts
      const clientIds = [selectedClient.id];
      if (selectedSecondaryApplicant) {
        clientIds.push(selectedSecondaryApplicant.id);
      }

      const { data, error } = await supabase
        .from('sb_accounts')
        .select(`
          id,
          sb_account_number,
          account_type,
          status,
          client_sb_accounts (
            role,
            client_id,
            clients (
              id,
              first_name,
              last_name
            )
          )
        `)
        .eq('is_deleted', false)
        .eq('status', 'active');

      if (error) throw error;

      // Filter accounts that belong to selected clients
      const filteredAccounts = (data || []).filter(account => {
        return account.client_sb_accounts.some(csa =>
          clientIds.includes(csa.client_id)
        );
      });

      setSbAccounts(filteredAccounts);
    } catch (error) {
      console.error('Error fetching SB accounts:', error);
    }
  };

  const calculateMaturityDetails = () => {
    if (!selectedScheme || !amount || !investmentDate) return;

    const principal = parseFloat(amount);
    const rate = selectedScheme.interest_rate;
    const tenureMonths = selectedScheme.tenure_months;

    // Maturity Date
    const maturity = new Date(investmentDate);
    maturity.setMonth(maturity.getMonth() + tenureMonths);
    setMaturityDate(maturity);

    // Monthly interest rate
    const monthlyRate = rate / 100 / 12;

    // Maturity Amount
    const maturityAmt = principal + (principal * monthlyRate * tenureMonths);
    setMaturityAmount(maturityAmt);

    // Commission
    const commissionAmt = (principal * selectedScheme.commission_percentage) / 100;
    setCommission(commissionAmt);

    // TDS on commission
    const tds = (commissionAmt * tdsPercentage) / 100;
    setTdsAmount(tds);

    // Actual Profit = Commission - TDS
    const actualProfitAmt = commissionAmt - tds;
    setActualProfit(actualProfitAmt);
  };

  const generateReferenceNumber = () => {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000);
    return `INV-${timestamp}-${random}`;
  };

  // Format dates to avoid timezone issues
  const formatDateForDB = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  const handleNextStep = () => {
    if (!selectedClient || !selectedScheme || !amount || !investmentDate || !startDate) {
      toast({
        title: "Error",
        description: "Please fill all required fields",
        variant: "destructive",
      });
      return;
    }
    if (amountError) {
      toast({
        title: "Invalid Amount",
        description: amountError,
        variant: "destructive",
      });
      return;
    }
    setCurrentStep(2);
  };

  const createNewSbAccount = async () => {
    if (!newAccountNumber || !selectedClient) return null;

    try {
      const { data: sbAccount, error: sbError } = await supabase
        .from('sb_accounts')
        .insert({
          sb_account_number: newAccountNumber,
          account_type: 'single',
          already_opened: true,
          opened_by_agency: false,
          opening_balance: null,
          status: 'active',
          closed_at: null,
          opened_at: null,
          remarks: null
        })
        .select()
        .single();

      if (sbError) throw sbError;

      // Create client_sb_accounts entry
      const { error: clientSbError } = await supabase
        .from('client_sb_accounts')
        .insert({
          client_id: selectedClient.id,
          sb_account_id: sbAccount.id,
          role: 'primary'
        });

      if (clientSbError) throw clientSbError;

      return sbAccount.id;
    } catch (error) {
      console.error('Error creating SB account:', error);
      throw error;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!paymentMode || !referenceNumber) {
      toast({
        title: "Error",
        description: "Please fill all transaction fields",
        variant: "destructive",
      });
      return;
    }

    if (paymentMode === 'sb_account' && !selectedSbAccount && !newAccountNumber) {
      toast({
        title: "Error",
        description: "Please select an SB account or enter new account number",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      let sbAccountId = selectedSbAccount;

      // Create new SB account if needed
      if (paymentMode === 'sb_account' && showNewAccountForm) {
        sbAccountId = await createNewSbAccount();
      }

      // If this is a reinvestment, create maturity payout transaction first
      if (isReinvestment && originalInvestment) {
        const { error: payoutError } = await supabase
          .from('transactions')
          .insert({
            investment_id: originalInvestment.id,
            amount_type: 'maturity_payout',
            amount: originalInvestment.maturity_amount,
            transaction_date: formatDateForDB(new Date()),
            reference_number: `MAT-${Date.now()}`,
            remark: `Maturity payout for reinvestment - ${originalInvestment.scheme_name}`
          });

        if (payoutError) throw payoutError;

        // Update original investment status
        const { error: updateError } = await supabase
          .from('investments')
          .update({ status: 'reinvested' })
          .eq('id', originalInvestment.id);

        if (updateError) throw updateError;
      }

      const investmentData = {
        client_id: selectedClient.id,
        second_applicant_id: selectedSecondaryApplicant?.id || null,
        scheme_id: selectedScheme.id,
        nominee_id: selectedNominee?.id || null,
        amount: parseFloat(amount),
        investment_date: formatDateForDB(investmentDate),
        start_date: formatDateForDB(startDate),
        maturity_date: maturityDate ? formatDateForDB(maturityDate) : null,
        maturity_amount: maturityAmount,
        investment_type: 'lumpsum',
        remark: remark || null,
        actual_profit: actualProfit,
        tds_amount: tdsAmount,
        reinvestment_source_id: reinvestmentSourceId,
        // Store scheme details
        scheme_name: selectedScheme.name,
        scheme_code: selectedScheme.scheme_code,
        interest_rate: selectedScheme.interest_rate,
        interest_type: selectedScheme.interest_type,
        tenure_months: selectedScheme.tenure_months,
        min_amount: selectedScheme.min_amount,
        max_amount: selectedScheme.max_amount,
        lock_in_period_months: selectedScheme.lock_in_period_months,
        commission_percentage: selectedScheme.commission_percentage,
        compounding_frequency: selectedScheme.compounding_frequency,
        payout_status: selectedScheme.payout_type,
        supports_sip: selectedScheme.supports_sip,
        status: 'active'
      };

      const { data: investment, error } = await supabase
        .from('investments')
        .insert([investmentData])
        .select()
        .single();

      if (error) throw error;

      // If this is a reinvestment, update the source investment status
      if (isReinvestment && reinvestmentSourceId) {
        const { error: updateError } = await supabase
          .from('investments')
          .update({ status: 'reinvested' })
          .eq('id', reinvestmentSourceId);

        if (updateError) {
          console.error('Error updating source investment status:', updateError);
        }
      }

      // Create transaction record with user-provided details
      const transactionData = {
        investment_id: investment.id,
        amount_type: 'investment',
        amount: parseFloat(amount),
        transaction_date: formatDateForDB(investmentDate),
        reference_number: referenceNumber,
        payment_mode: paymentMode,
        sb_account_id: paymentMode === 'sb_account' ? sbAccountId : null,
        remark: isReinvestment ? `Reinvestment in ${selectedScheme.name}` : `Initial investment in ${selectedScheme.name}`
      };

      console.log('Creating transaction with data:', transactionData);

      const { data: transaction, error: transactionError } = await supabase
        .from('transactions')
        .insert([transactionData])
        .select()
        .single();

      if (transactionError) {
        console.error('Transaction creation error:', transactionError);
        throw new Error(`Failed to create transaction: ${transactionError.message}`);
      }

      console.log('Transaction created successfully:', transaction);

      toast({
        title: "Success",
        description: isReinvestment ? "Reinvestment and transaction created successfully" : "Investment and transaction created successfully",
      });

      navigate('/investments');
    } catch (error) {
      console.error('Error creating investment:', error);
      toast({
        title: "Error",
        description: "Failed to create investment",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const renderInvestmentForm = () => (
    <form onSubmit={(e) => { e.preventDefault(); handleNextStep(); }}>
      <Card>
        <CardHeader>
          <CardTitle>Investment Details</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Client Selection */}
          <div className="space-y-2">
            <Label>Primary Client *</Label>
            <ClientSelector
              selectedClient={selectedClient}
              onClientSelect={setSelectedClient}
              required
              disabled={isReinvestment}
            />
          </div>

          {/* Secondary Applicant */}
          <div className="space-y-2">
            <Label>Secondary Applicant (Optional)</Label>
            <ClientSelector
              selectedClient={selectedSecondaryApplicant}
              onClientSelect={setSelectedSecondaryApplicant}
              required={false}
            />
          </div>

          {/* Scheme Selection */}
          <div className="space-y-2">
            <Label>Investment Scheme *</Label>
            <SchemeSelector
              selectedScheme={selectedScheme}
              onSchemeSelect={setSelectedScheme}
            />
          </div>

          {/* Show scheme details if selected */}
          {selectedScheme && (
            <div className="bg-gray-50 p-4 rounded-lg space-y-2">
              <h4 className="font-medium">Scheme Details</h4>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                <div><strong>Interest Rate:</strong> {selectedScheme.interest_rate}%</div>
                <div><strong>Tenure:</strong> {selectedScheme.tenure_months} months</div>
                <div><strong>Interest Type:</strong> {selectedScheme.interest_type}</div>
                <div><strong>Min Amount:</strong> ₹{selectedScheme.min_amount?.toLocaleString()}</div>
                <div><strong>Max Amount:</strong> ₹{selectedScheme.max_amount?.toLocaleString()}</div>
                <div><strong>Commission:</strong> {selectedScheme.commission_percentage}%</div>
              </div>
            </div>
          )}

          {/* Nominee Selection */}
          {selectedClient && (
            <div className="space-y-2">
              <Label>Nominee (Optional)</Label>
              <NomineeSelector
                clientId={selectedClient.id}
                selectedNominee={selectedNominee}
                onNomineeSelect={setSelectedNominee}
              />
            </div>
          )}

          {/* Amount */}
          <div className="space-y-2">
            <Label htmlFor="amount">Investment Amount *</Label>
            <Input
  id="amount"
  type="number"
  onWheel={(e) => e.currentTarget.blur()} // 🔒 prevent scroll-triggered changes
  placeholder="Enter investment amount"
  value={amount}
  onChange={(e) => {
    const input = e.target.value;
    setAmount(input);

    const numericAmount = parseFloat(input);
    if (selectedScheme && numericAmount < selectedScheme.min_amount) {
      setAmountError(`Amount must be at least ₹${selectedScheme.min_amount.toLocaleString()}`);
    } else {
      setAmountError(null);
    }
  }}
  required
  disabled={isReinvestment}
/>

            {amountError && (
              <p className="text-sm text-red-500 mt-1">{amountError}</p>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Investment Date */}
            <div className="space-y-2">
              <Label>Investment Date *</Label>
              <EnhancedDatePicker
                date={investmentDate}
                onSelect={setInvestmentDate}
                placeholder="Select investment date"
              />
            </div>

            {/* Start Date */}
            <div className="space-y-2">
              <Label>Start Date *</Label>
              <EnhancedDatePicker
                date={startDate}
                onSelect={setStartDate}
                placeholder="Select start date"
              />
            </div>
          </div>

          {/* Calculated Fields */}
          {maturityDate && (
            <div className="bg-blue-50 p-4 rounded-lg space-y-2">
              <h4 className="font-medium">Calculated Details</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div><strong>Maturity Date:</strong> {maturityDate.toLocaleDateString()}</div>
                <div><strong>Maturity Amount:</strong> ₹{maturityAmount.toLocaleString()}</div>
                <div><strong>Commission:</strong> ₹{commission.toLocaleString()}</div>
                <div><strong>TDS Amount:</strong> ₹{tdsAmount.toLocaleString()}</div>
                <div><strong>Actual Profit:</strong> ₹{actualProfit.toLocaleString()}</div>
              </div>
            </div>
          )}

          {/* Investment Type (Fixed) */}
          <div className="space-y-2">
            <Label>Investment Type</Label>
            <Input value="Lumpsum" disabled />
          </div>

          {/* Remark */}
          <div className="space-y-2">
            <Label htmlFor="remark">Remark (Optional)</Label>
            <Textarea
              id="remark"
              placeholder="Enter any remarks"
              value={remark}
              onChange={(e) => setRemark(e.target.value)}
            />
          </div>

          <div className="flex justify-end gap-4">
            <Button type="button" variant="outline" onClick={() => navigate('/investments')}>
              Cancel
            </Button>
            <Button type="submit" disabled={!!amountError}>
              <ArrowRight className="h-4 w-4 mr-2" />
              Next: Transaction Details
            </Button>
          </div>
        </CardContent>
      </Card>
    </form>
  );

  const renderTransactionForm = () => (
    <form onSubmit={handleSubmit}>
      <Card>
        <CardHeader>
          <CardTitle>Transaction Details</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Pre-filled Investment Summary */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium mb-2">Investment Summary</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div><strong>Client:</strong> {selectedClient?.first_name} {selectedClient?.last_name}</div>
              <div><strong>Scheme:</strong> {selectedScheme?.name}</div>
              <div><strong>Amount:</strong> ₹{parseFloat(amount).toLocaleString()}</div>
              <div><strong>Type:</strong> Investment</div>
            </div>
          </div>

          {/* Payment Mode */}
          <div className="space-y-2">
            <Label>Payment Mode *</Label>
            <Select value={paymentMode} onValueChange={setPaymentMode}>
              <SelectTrigger>
                <SelectValue placeholder="Select payment mode" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="sb_account">SB Account</SelectItem>
                <SelectItem value="ecs">ECS</SelectItem>
                <SelectItem value="cheque">Cheque</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* SB Account Selection */}
          {paymentMode === 'sb_account' && (
            <div className="space-y-2">
              <Label>SB Account</Label>
              <Select value={selectedSbAccount} onValueChange={(value) => {
                if (value === 'new') {
                  setShowNewAccountForm(true);
                  setSelectedSbAccount('');
                } else {
                  setShowNewAccountForm(false);
                  setSelectedSbAccount(value);
                }
              }}>
                <SelectTrigger>
                  <SelectValue placeholder="Select SB account" />
                </SelectTrigger>
                <SelectContent>
                  {sbAccounts.map((account) => {
                    const accountHolders = account.client_sb_accounts.map(csa =>
                      `${csa.clients?.first_name} ${csa.clients?.last_name}`
                    ).join(', ');
                    const accountType = account.account_type === 'joint' ? ' (Joint)' : ' (Single)';

                    return (
                      <SelectItem key={account.id} value={account.id}>
                        {account.sb_account_number} - {accountHolders}{accountType}
                      </SelectItem>
                    );
                  })}
                  <SelectItem value="new">+ Add New SB Account</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          {/* New SB Account Form */}
          {showNewAccountForm && (
            <div className="space-y-2 p-4 border rounded-lg bg-blue-50">
              <Label>New SB Account Number *</Label>
              <Input
                placeholder="Enter SB account number"
                value={newAccountNumber}
                onChange={(e) => setNewAccountNumber(e.target.value)}
                required
              />
              <p className="text-sm text-blue-600">This will create a new SB account for the selected client.</p>
            </div>
          )}

          {/* Reference Number */}
          <div className="space-y-2">
            <Label>Reference Number / Cheque Number *</Label>
            <Input
              placeholder="Enter reference or cheque number"
              value={referenceNumber}
              onChange={(e) => setReferenceNumber(e.target.value)}
              required
            />
          </div>

          <div className="flex justify-between gap-4">
            <Button type="button" variant="outline" onClick={() => setCurrentStep(1)}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Investment
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Creating...' : 'Create Investment'}
            </Button>
          </div>
        </CardContent>
      </Card>
    </form>
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button variant="ghost" onClick={() => navigate('/investments')}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-3xl font-bold">
          {isReinvestment ? 'Create Reinvestment' : 'Add New Investment'}
          <span className="text-lg font-normal text-gray-500 ml-2">
            Step {currentStep} of 2
          </span>
        </h1>
      </div>

      {isReinvestment && (
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-4">
            <p className="text-blue-800 text-sm">
              This is a reinvestment from a matured investment. The amount is pre-filled from the maturity amount.
            </p>
          </CardContent>
        </Card>
      )}

      {currentStep === 1 ? renderInvestmentForm() : renderTransactionForm()}
    </div>
  );
};

export default InvestmentForm;
