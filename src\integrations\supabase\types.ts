export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      alerts: {
        Row: {
          alert_date: string
          alert_type: string
          channel: string
          created_at: string | null
          id: string
          investment_id: string
          message: string
          status: string | null
          updated_at: string | null
        }
        Insert: {
          alert_date: string
          alert_type: string
          channel: string
          created_at?: string | null
          id?: string
          investment_id: string
          message: string
          status?: string | null
          updated_at?: string | null
        }
        Update: {
          alert_date?: string
          alert_type?: string
          channel?: string
          created_at?: string | null
          id?: string
          investment_id?: string
          message?: string
          status?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "alerts_investment_id_fkey"
            columns: ["investment_id"]
            isOneToOne: false
            referencedRelation: "investments"
            referencedColumns: ["id"]
          },
        ]
      }
      client_sb_accounts: {
        Row: {
          added_at: string
          client_id: string
          id: string
          role: string
          sb_account_id: string
        }
        Insert: {
          added_at?: string
          client_id: string
          id?: string
          role?: string
          sb_account_id: string
        }
        Update: {
          added_at?: string
          client_id?: string
          id?: string
          role?: string
          sb_account_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "client_sb_accounts_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "clients"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "client_sb_accounts_sb_account_id_fkey"
            columns: ["sb_account_id"]
            isOneToOne: false
            referencedRelation: "sb_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      clients: {
        Row: {
          aadhar_number: string
          aadhar_photo_url: string | null
          address: string | null
          care_of: string | null
          cif_id: string | null
          city: string | null
          client_photo_url: string | null
          contact_person2: string | null
          created_at: string | null
          email: string | null
          first_name: string
          id: string
          is_deleted: boolean | null
          last_name: string
          mobile_number: string
          pan_card_number: string
          pan_photo_url: string | null
          pincode: string | null
          state: string | null
          updated_at: string | null
          village: string | null
        }
        Insert: {
          aadhar_number: string
          aadhar_photo_url?: string | null
          address?: string | null
          care_of?: string | null
          cif_id?: string | null
          city?: string | null
          client_photo_url?: string | null
          contact_person2?: string | null
          created_at?: string | null
          email?: string | null
          first_name: string
          id?: string
          is_deleted?: boolean | null
          last_name: string
          mobile_number: string
          pan_card_number: string
          pan_photo_url?: string | null
          pincode?: string | null
          state?: string | null
          updated_at?: string | null
          village?: string | null
        }
        Update: {
          aadhar_number?: string
          aadhar_photo_url?: string | null
          address?: string | null
          care_of?: string | null
          cif_id?: string | null
          city?: string | null
          client_photo_url?: string | null
          contact_person2?: string | null
          created_at?: string | null
          email?: string | null
          first_name?: string
          id?: string
          is_deleted?: boolean | null
          last_name?: string
          mobile_number?: string
          pan_card_number?: string
          pan_photo_url?: string | null
          pincode?: string | null
          state?: string | null
          updated_at?: string | null
          village?: string | null
        }
        Relationships: []
      }
      investments: {
        Row: {
          actual_profit: number
          amount: number
          client_id: string
          commission_percentage: number | null
          compounding_frequency: string | null
          created_at: string | null
          id: string
          interest_rate: number
          interest_type: string
          investment_date: string
          investment_type: string | null
          is_active: boolean | null
          lock_in_period_months: number
          maturity_amount: number
          maturity_date: string
          max_amount: number | null
          min_amount: number | null
          nominee_id: string | null
          payout_status: string | null
          reinvestment_source_id: string | null
          remark: string | null
          scheme_code: string
          scheme_id: string
          scheme_name: string
          second_applicant_id: string | null
          sip_amount: number | null
          sip_frequency: string | null
          sip_installments: number | null
          sip_next_due: string | null
          start_date: string
          status: string | null
          supports_sip: boolean | null
          tds_amount: number | null
          tenure_months: number | null
          updated_at: string | null
        }
        Insert: {
          actual_profit: number
          amount: number
          client_id: string
          commission_percentage?: number | null
          compounding_frequency?: string | null
          created_at?: string | null
          id?: string
          interest_rate: number
          interest_type: string
          investment_date: string
          investment_type?: string | null
          is_active?: boolean | null
          lock_in_period_months: number
          maturity_amount: number
          maturity_date: string
          max_amount?: number | null
          min_amount?: number | null
          nominee_id?: string | null
          payout_status?: string | null
          reinvestment_source_id?: string | null
          remark?: string | null
          scheme_code: string
          scheme_id: string
          scheme_name: string
          second_applicant_id?: string | null
          sip_amount?: number | null
          sip_frequency?: string | null
          sip_installments?: number | null
          sip_next_due?: string | null
          start_date: string
          status?: string | null
          supports_sip?: boolean | null
          tds_amount?: number | null
          tenure_months?: number | null
          updated_at?: string | null
        }
        Update: {
          actual_profit?: number
          amount?: number
          client_id?: string
          commission_percentage?: number | null
          compounding_frequency?: string | null
          created_at?: string | null
          id?: string
          interest_rate?: number
          interest_type?: string
          investment_date?: string
          investment_type?: string | null
          is_active?: boolean | null
          lock_in_period_months?: number
          maturity_amount?: number
          maturity_date?: string
          max_amount?: number | null
          min_amount?: number | null
          nominee_id?: string | null
          payout_status?: string | null
          reinvestment_source_id?: string | null
          remark?: string | null
          scheme_code?: string
          scheme_id?: string
          scheme_name?: string
          second_applicant_id?: string | null
          sip_amount?: number | null
          sip_frequency?: string | null
          sip_installments?: number | null
          sip_next_due?: string | null
          start_date?: string
          status?: string | null
          supports_sip?: boolean | null
          tds_amount?: number | null
          tenure_months?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "investments_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "clients"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "investments_nominee_id_fkey"
            columns: ["nominee_id"]
            isOneToOne: false
            referencedRelation: "nominees"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "investments_reinvestment_source_id_fkey"
            columns: ["reinvestment_source_id"]
            isOneToOne: false
            referencedRelation: "investments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "investments_scheme_id_fkey"
            columns: ["scheme_id"]
            isOneToOne: false
            referencedRelation: "schemes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "investments_second_applicant_id_fkey"
            columns: ["second_applicant_id"]
            isOneToOne: false
            referencedRelation: "clients"
            referencedColumns: ["id"]
          },
        ]
      }
      nominees: {
        Row: {
          birthdate: string | null
          client_id: string | null
          created_at: string | null
          document_url: string | null
          id: string
          name: string
          relation: string
          updated_at: string | null
        }
        Insert: {
          birthdate?: string | null
          client_id?: string | null
          created_at?: string | null
          document_url?: string | null
          id?: string
          name: string
          relation: string
          updated_at?: string | null
        }
        Update: {
          birthdate?: string | null
          client_id?: string | null
          created_at?: string | null
          document_url?: string | null
          id?: string
          name?: string
          relation?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "nominees_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "clients"
            referencedColumns: ["id"]
          },
        ]
      }
      notification_settings: {
        Row: {
          alert_days_before: number | null
          created_at: string | null
          default_commission: number | null
          default_email_from: string | null
          email_enabled: boolean | null
          id: string
          notification_time: string | null
          sms_enabled: boolean | null
          smtp_host: string | null
          smtp_password: string | null
          smtp_port: number | null
          smtp_username: string | null
          tds_percentage: number | null
          updated_at: string | null
          whatsapp_enabled: boolean | null
        }
        Insert: {
          alert_days_before?: number | null
          created_at?: string | null
          default_commission?: number | null
          default_email_from?: string | null
          email_enabled?: boolean | null
          id?: string
          notification_time?: string | null
          sms_enabled?: boolean | null
          smtp_host?: string | null
          smtp_password?: string | null
          smtp_port?: number | null
          smtp_username?: string | null
          tds_percentage?: number | null
          updated_at?: string | null
          whatsapp_enabled?: boolean | null
        }
        Update: {
          alert_days_before?: number | null
          created_at?: string | null
          default_commission?: number | null
          default_email_from?: string | null
          email_enabled?: boolean | null
          id?: string
          notification_time?: string | null
          sms_enabled?: boolean | null
          smtp_host?: string | null
          smtp_password?: string | null
          smtp_port?: number | null
          smtp_username?: string | null
          tds_percentage?: number | null
          updated_at?: string | null
          whatsapp_enabled?: boolean | null
        }
        Relationships: []
      }
      role_permissions: {
        Row: {
          can_add: boolean | null
          can_delete: boolean | null
          can_edit: boolean | null
          can_view: boolean | null
          created_at: string | null
          id: string
          module: string
          role_id: string | null
          updated_at: string | null
        }
        Insert: {
          can_add?: boolean | null
          can_delete?: boolean | null
          can_edit?: boolean | null
          can_view?: boolean | null
          created_at?: string | null
          id?: string
          module: string
          role_id?: string | null
          updated_at?: string | null
        }
        Update: {
          can_add?: boolean | null
          can_delete?: boolean | null
          can_edit?: boolean | null
          can_view?: boolean | null
          created_at?: string | null
          id?: string
          module?: string
          role_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "role_permissions_role_id_fkey"
            columns: ["role_id"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["id"]
          },
        ]
      }
      roles: {
        Row: {
          created_at: string | null
          description: string | null
          id: string
          name: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: string
          name: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: string
          name?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      sb_accounts: {
        Row: {
          account_type: string
          already_opened: boolean
          closed_at: string | null
          created_at: string
          id: string
          is_deleted: boolean
          opened_at: string | null
          opened_by_agency: boolean
          opening_balance: number | null
          remarks: string | null
          sb_account_number: string
          status: string
          updated_at: string
        }
        Insert: {
          account_type: string
          already_opened?: boolean
          closed_at?: string | null
          created_at?: string
          id?: string
          is_deleted?: boolean
          opened_at?: string | null
          opened_by_agency?: boolean
          opening_balance?: number | null
          remarks?: string | null
          sb_account_number: string
          status?: string
          updated_at?: string
        }
        Update: {
          account_type?: string
          already_opened?: boolean
          closed_at?: string | null
          created_at?: string
          id?: string
          is_deleted?: boolean
          opened_at?: string | null
          opened_by_agency?: boolean
          opening_balance?: number | null
          remarks?: string | null
          sb_account_number?: string
          status?: string
          updated_at?: string
        }
        Relationships: []
      }
      schemes: {
        Row: {
          commission_percentage: number | null
          compounding_frequency: string | null
          created_at: string | null
          id: string
          interest_rate: number
          interest_type: string
          is_active: boolean | null
          lock_in_period_months: number | null
          max_amount: number | null
          max_sip_amount: number | null
          min_amount: number
          min_sip_amount: number | null
          name: string
          payout_type: string
          scheme_code: string
          supports_sip: boolean | null
          tenure_months: number
          updated_at: string | null
        }
        Insert: {
          commission_percentage?: number | null
          compounding_frequency?: string | null
          created_at?: string | null
          id?: string
          interest_rate: number
          interest_type: string
          is_active?: boolean | null
          lock_in_period_months?: number | null
          max_amount?: number | null
          max_sip_amount?: number | null
          min_amount: number
          min_sip_amount?: number | null
          name: string
          payout_type: string
          scheme_code: string
          supports_sip?: boolean | null
          tenure_months: number
          updated_at?: string | null
        }
        Update: {
          commission_percentage?: number | null
          compounding_frequency?: string | null
          created_at?: string | null
          id?: string
          interest_rate?: number
          interest_type?: string
          is_active?: boolean | null
          lock_in_period_months?: number | null
          max_amount?: number | null
          max_sip_amount?: number | null
          min_amount?: number
          min_sip_amount?: number | null
          name?: string
          payout_type?: string
          scheme_code?: string
          supports_sip?: boolean | null
          tenure_months?: number
          updated_at?: string | null
        }
        Relationships: []
      }
      transactions: {
        Row: {
          amount: number
          amount_type: string
          created_at: string | null
          id: string
          investment_id: string | null
          payment_mode: string | null
          reference_number: string
          remark: string | null
          sb_account_id: string | null
          transaction_date: string
          updated_at: string | null
        }
        Insert: {
          amount: number
          amount_type: string
          created_at?: string | null
          id?: string
          investment_id?: string | null
          payment_mode?: string | null
          reference_number: string
          remark?: string | null
          sb_account_id?: string | null
          transaction_date: string
          updated_at?: string | null
        }
        Update: {
          amount?: number
          amount_type?: string
          created_at?: string | null
          id?: string
          investment_id?: string | null
          payment_mode?: string | null
          reference_number?: string
          remark?: string | null
          sb_account_id?: string | null
          transaction_date?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "transactions_investment_id_fkey"
            columns: ["investment_id"]
            isOneToOne: false
            referencedRelation: "investments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "transactions_sb_account_id_fkey"
            columns: ["sb_account_id"]
            isOneToOne: false
            referencedRelation: "sb_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      users: {
        Row: {
          created_at: string | null
          id: string
          is_deleted: boolean | null
          role_id: string | null
          updated_at: string | null
          username: string | null
        }
        Insert: {
          created_at?: string | null
          id: string
          is_deleted?: boolean | null
          role_id?: string | null
          updated_at?: string | null
          username?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          is_deleted?: boolean | null
          role_id?: string | null
          updated_at?: string | null
          username?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "users_role_id_fkey"
            columns: ["role_id"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      get_user_role: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      is_admin: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      update_matured_investments: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
