
import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';

interface Permission {
  module: string;
  can_view: boolean;
  can_add: boolean;
  can_edit: boolean;
  can_delete: boolean;
}

export const usePermissions = () => {
  const { user, userRole } = useAuth();
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchPermissions = async () => {
      if (!user || !userRole) {
        setPermissions([]);
        setLoading(false);
        return;
      }

      try {
        // Get role ID first
        const { data: roleData, error: roleError } = await supabase
          .from('roles')
          .select('id')
          .eq('name', userRole)
          .single();

        if (roleError || !roleData) {
          console.error('Error fetching role:', roleError);
          setPermissions([]);
          setLoading(false);
          return;
        }

        // Get permissions for this role
        const { data: permissionsData, error: permissionsError } = await supabase
          .from('role_permissions')
          .select('module, can_view, can_add, can_edit, can_delete')
          .eq('role_id', roleData.id);

        if (permissionsError) {
          console.error('Error fetching permissions:', permissionsError);
          setPermissions([]);
        } else {
          setPermissions(permissionsData || []);
        }
      } catch (error) {
        console.error('Error in fetchPermissions:', error);
        setPermissions([]);
      } finally {
        setLoading(false);
      }
    };

    fetchPermissions();
  }, [user, userRole]);

  const hasPermission = (module: string, action: 'view' | 'add' | 'edit' | 'delete'): boolean => {
    // Admin has all permissions
    if (userRole === 'Admin') return true;
    
    const permission = permissions.find(p => p.module === module);
    if (!permission) return false;

    switch (action) {
      case 'view':
        return permission.can_view;
      case 'add':
        return permission.can_add;
      case 'edit':
        return permission.can_edit;
      case 'delete':
        return permission.can_delete;
      default:
        return false;
    }
  };

  const canViewModule = (module: string): boolean => {
    return hasPermission(module, 'view');
  };

  return {
    permissions,
    loading,
    hasPermission,
    canViewModule,
  };
};
