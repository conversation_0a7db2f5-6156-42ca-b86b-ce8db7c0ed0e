
-- First, let's update the investment status to support new states
ALTER TABLE public.investments 
DROP CONSTRAINT IF EXISTS investments_status_check;

ALTER TABLE public.investments 
ADD CONSTRAINT investments_status_check 
CHECK (status IN ('active', 'matured', 'withdrawn', 'transferred', 'reinvested'));

-- Enable pg_cron extension for scheduled jobs
CREATE EXTENSION IF NOT EXISTS pg_cron;

-- Create a function to update matured investments
CREATE OR REPLACE FUNCTION public.update_matured_investments()
RETURNS void
LANGUAGE sql
SECURITY DEFINER
AS $$
  UPDATE public.investments 
  SET status = 'matured', updated_at = NOW()
  WHERE status = 'active' 
    AND maturity_date <= CURRENT_DATE 
    AND is_active = true;
$$;

-- Schedule the cron job to run daily at 9 AM
SELECT cron.schedule(
  'update-matured-investments',
  '0 9 * * *', -- Daily at 9 AM
  $$
  SELECT public.update_matured_investments();
  $$
);

-- Create a function to check if user is admin
CREATE OR REPLACE FUNCTION public.is_admin()
RETURNS boolean
LANGUAGE sql
STABLE
SECURITY DEFINER
AS $$
  SELECT EXISTS (
    SELECT 1 FROM public.users u 
    JOIN public.roles r ON u.role_id = r.id 
    WHERE u.id = auth.uid() AND r.name = 'Admin'
  );
$$;

-- Add RLS policy for notification_settings (admin only)
DROP POLICY IF EXISTS "Admin only access" ON public.notification_settings;
CREATE POLICY "Admin only access" ON public.notification_settings 
FOR ALL USING (public.is_admin());

-- Add RLS policy for role_permissions (admin only)
DROP POLICY IF EXISTS "Admin only access" ON public.role_permissions;
CREATE POLICY "Admin only access" ON public.role_permissions 
FOR ALL USING (public.is_admin());

-- Add RLS policy for roles (admin only)
DROP POLICY IF EXISTS "Admin only access" ON public.roles;
CREATE POLICY "Admin only access" ON public.roles 
FOR ALL USING (public.is_admin());
