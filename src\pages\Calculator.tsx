
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Calculator as CalculatorIcon, TrendingUp } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';

interface Scheme {
  id: string;
  name: string;
  scheme_code: string;
  interest_rate: number;
  interest_type: string;
  tenure_months: number;
  compounding_frequency: string;
}

interface CalculationResult {
  principal: number;
  interestRate: number;
  tenureMonths: number;
  maturityAmount: number;
  totalInterest: number;
  monthlyInterest: number;
  yearlyInterest: number;
  interestType?: string;
  compoundingFrequency?: string;
}

const Calculator: React.FC = () => {
  const [schemes, setSchemes] = useState<Scheme[]>([]);
  const [selectedScheme, setSelectedScheme] = useState<Scheme | null>(null);

  // Scheme-wise calculator state
  const [schemeAmount, setSchemeAmount] = useState<string>('');
  const [schemeResult, setSchemeResult] = useState<CalculationResult | null>(null);

  // Custom calculator state
  const [customAmount, setCustomAmount] = useState<string>('');
  const [customRate, setCustomRate] = useState<string>('');
  const [customTenure, setCustomTenure] = useState<string>('');
  const [customInterestType, setCustomInterestType] = useState<string>('simple');
  const [customCompoundingFrequency, setCustomCompoundingFrequency] = useState<string>('annually');
  const [customResult, setCustomResult] = useState<CalculationResult | null>(null);

  useEffect(() => {
    fetchSchemes();
  }, []);

  const fetchSchemes = async () => {
    try {
      const { data, error } = await supabase
        .from('schemes')
        .select('*')
        .eq('is_active', true)
        .order('name');

      if (error) throw error;
      setSchemes(data || []);
    } catch (error) {
      console.error('Error fetching schemes:', error);
    }
  };

  const calculateMaturity = (
    principal: number,
    rate: number,
    tenureMonths: number,
    interestType: string = 'simple',
    compoundingFrequency: string = 'annually'
  ): CalculationResult => {
    const yearlyRate = rate / 100;
    const tenureYears = tenureMonths / 12;

    let maturityAmount: number;
    let totalInterest: number;
    let monthlyInterest: number;
    let yearlyInterest: number;

    if (interestType === 'compound') {
      // Compound Interest Calculation
      let compoundingPeriodsPerYear: number;

      switch (compoundingFrequency) {
        case 'monthly':
          compoundingPeriodsPerYear = 12;
          break;
        case 'quarterly':
          compoundingPeriodsPerYear = 4;
          break;
        case 'semi-annually':
          compoundingPeriodsPerYear = 2;
          break;
        case 'annually':
        default:
          compoundingPeriodsPerYear = 1;
          break;
      }

      // A = P(1 + r/n)^(nt)
      const ratePerPeriod = yearlyRate / compoundingPeriodsPerYear;
      const totalPeriods = compoundingPeriodsPerYear * tenureYears;

      maturityAmount = principal * Math.pow(1 + ratePerPeriod, totalPeriods);
      totalInterest = maturityAmount - principal;

      // For compound interest, monthly interest varies, so we calculate average
      monthlyInterest = totalInterest / tenureMonths;
      yearlyInterest = totalInterest / tenureYears;

    } else {
      // Simple Interest Calculation
      // SI = P * R * T / 100 (where T is in years)
      totalInterest = principal * yearlyRate * tenureYears;
      maturityAmount = principal + totalInterest;

      // For simple interest, monthly interest is constant
      monthlyInterest = totalInterest / tenureMonths;
      yearlyInterest = principal * yearlyRate;
    }

    return {
      principal,
      interestRate: rate,
      tenureMonths,
      maturityAmount: Math.round(maturityAmount * 100) / 100,
      totalInterest: Math.round(totalInterest * 100) / 100,
      monthlyInterest: Math.round(monthlyInterest * 100) / 100,
      yearlyInterest: Math.round(yearlyInterest * 100) / 100,
      interestType,
      compoundingFrequency
    };
  };

  const handleSchemeCalculation = () => {
    if (!selectedScheme || !schemeAmount) return;

    const principal = parseFloat(schemeAmount);
    const result = calculateMaturity(
      principal,
      selectedScheme.interest_rate,
      selectedScheme.tenure_months,
      selectedScheme.interest_type,
      selectedScheme.compounding_frequency || 'annually'
    );

    setSchemeResult(result);
  };

  const handleCustomCalculation = () => {
    if (!customAmount || !customRate || !customTenure) return;

    const principal = parseFloat(customAmount);
    const rate = parseFloat(customRate);
    const tenure = parseInt(customTenure);

    const result = calculateMaturity(
      principal,
      rate,
      tenure,
      customInterestType,
      customCompoundingFrequency
    );
    setCustomResult(result);
  };

  const handleSchemeSelect = (schemeId: string) => {
    const scheme = schemes.find(s => s.id === schemeId);
    setSelectedScheme(scheme || null);
  };

  const ResultCard = ({ result }: { result: CalculationResult | null }) => {
    if (!result) return null;

    const getCalculationFormula = () => {
      if (result.interestType === 'compound') {
        return `A = P(1 + r/n)^(nt)`;
      } else {
        return `A = P + (P × R × T)`;
      }
    };

    const getCalculationDescription = () => {
      if (result.interestType === 'compound') {
        return `Compound Interest calculated ${result.compoundingFrequency || 'annually'}`;
      } else {
        return `Simple Interest calculation`;
      }
    };

    return (
      <Card className="mt-6">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Calculation Results
          </CardTitle>
          {result.interestType && (
            <div className="text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
              <div className="font-medium text-gray-800 mb-1">{getCalculationDescription()}</div>
              <div className="font-mono text-xs">Formula: {getCalculationFormula()}</div>
              {result.interestType === 'compound' && (
                <div className="text-xs mt-1">
                  Where: P = Principal, r = Annual rate, n = Compounding frequency per year, t = Time in years
                </div>
              )}
              {result.interestType === 'simple' && (
                <div className="text-xs mt-1">
                  Where: P = Principal, R = Annual rate, T = Time in years
                </div>
              )}
            </div>
          )}
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Principal Amount:</span>
                <span className="font-semibold">₹{result.principal.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Interest Rate:</span>
                <span className="font-semibold">{result.interestRate}% per annum</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Tenure:</span>
                <span className="font-semibold">{result.tenureMonths} months ({(result.tenureMonths / 12).toFixed(1)} years)</span>
              </div>
              {result.interestType && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Interest Type:</span>
                  <span className="font-semibold capitalize">{result.interestType}</span>
                </div>
              )}
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Total Interest:</span>
                <span className="font-semibold text-green-600">₹{result.totalInterest.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">
                  {result.interestType === 'compound' ? 'Avg Monthly Interest:' : 'Monthly Interest:'}
                </span>
                <span className="font-semibold">₹{result.monthlyInterest.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Yearly Interest:</span>
                <span className="font-semibold">₹{result.yearlyInterest.toLocaleString()}</span>
              </div>
              <div className="flex justify-between border-t pt-3">
                <span className="text-gray-900 font-medium">Maturity Amount:</span>
                <span className="font-bold text-blue-600 text-lg">₹{result.maturityAmount.toLocaleString()}</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <CalculatorIcon className="h-8 w-8" />
        <h1 className="text-3xl font-bold">Investment Calculator</h1>
      </div>

      <Tabs defaultValue="scheme" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="scheme">Scheme-wise Calculator</TabsTrigger>
          <TabsTrigger value="custom">Custom Calculator</TabsTrigger>
        </TabsList>

        <TabsContent value="scheme">
          <Card>
            <CardHeader>
              <CardTitle>Calculate Returns Based on Scheme</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label>Select Scheme</Label>
                <Select onValueChange={handleSchemeSelect}>
                  <SelectTrigger>
                    <SelectValue placeholder="Choose a scheme" />
                  </SelectTrigger>
                  <SelectContent>
                    {schemes.map((scheme) => (
                      <SelectItem key={scheme.id} value={scheme.id}>
                        {scheme.name} ({scheme.interest_rate}% - {scheme.tenure_months} months)
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {selectedScheme && (
                <div className="bg-gray-50 p-4 rounded-lg space-y-2">
                  <h4 className="font-medium">Scheme Details</h4>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div><strong>Interest Rate:</strong> {selectedScheme.interest_rate}%</div>
                    <div><strong>Tenure:</strong> {selectedScheme.tenure_months} months</div>
                    <div><strong>Interest Type:</strong> {selectedScheme.interest_type}</div>
                    <div><strong>Compounding:</strong> {selectedScheme.compounding_frequency}</div>
                  </div>
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="schemeAmount">Investment Amount</Label>
                <Input
                  id="schemeAmount"
                  type="number"
                  placeholder="Enter amount to invest"
                  value={schemeAmount}
                  onChange={(e) => setSchemeAmount(e.target.value)}
                />
              </div>

              <Button
                onClick={handleSchemeCalculation}
                disabled={!selectedScheme || !schemeAmount}
                className="w-full"
              >
                Calculate Returns
              </Button>

              <ResultCard result={schemeResult} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="custom">
          <Card>
            <CardHeader>
              <CardTitle>Custom Investment Calculator</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="customAmount">Principal Amount</Label>
                  <Input
                    id="customAmount"
                    type="number"
                    placeholder="Enter principal amount"
                    value={customAmount}
                    onChange={(e) => setCustomAmount(e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="customRate">Interest Rate (% per annum)</Label>
                  <Input
                    id="customRate"
                    type="number"
                    step="0.01"
                    placeholder="Enter interest rate"
                    value={customRate}
                    onChange={(e) => setCustomRate(e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="customTenure">Tenure (months)</Label>
                  <Input
                    id="customTenure"
                    type="number"
                    placeholder="Enter tenure in months"
                    value={customTenure}
                    onChange={(e) => setCustomTenure(e.target.value)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Interest Type</Label>
                  <Select value={customInterestType} onValueChange={setCustomInterestType}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select interest type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="simple">Simple Interest</SelectItem>
                      <SelectItem value="compound">Compound Interest</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {customInterestType === 'compound' && (
                  <div className="space-y-2">
                    <Label>Compounding Frequency</Label>
                    <Select value={customCompoundingFrequency} onValueChange={setCustomCompoundingFrequency}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select compounding frequency" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="annually">Annually</SelectItem>
                        <SelectItem value="semi-annually">Semi-Annually</SelectItem>
                        <SelectItem value="quarterly">Quarterly</SelectItem>
                        <SelectItem value="monthly">Monthly</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}
              </div>

              <Button
                onClick={handleCustomCalculation}
                disabled={!customAmount || !customRate || !customTenure}
                className="w-full"
              >
                Calculate Returns
              </Button>

              <ResultCard result={customResult} />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Calculator;
