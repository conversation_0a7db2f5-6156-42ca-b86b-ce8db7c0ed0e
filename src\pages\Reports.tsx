import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Download, FileSpreadsheet, Search, Filter, TrendingUp, Users, DollarSign, Calendar, ArrowUpDown } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import { Enhanced<PERSON>ate<PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, getDateRangeValues } from '@/components/ui/enhanced-date-filter';
import { debounce } from 'lodash';


interface ReportFilters {
  searchTerm: string;
  dateFrom: string;
  dateTo: string;
  status: string;
  scheme: string;
  client: string;
  amountFrom: string;
  amountTo: string;
  transactionType: string;
}


interface Client {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  mobile_number: string;
  aadhar_number: string;
  pan_card_number: string;
  sb_account_number: string;
  created_at: string;
  total_investment?: number;
}


interface Investment {
  id: string;
  client_id: string;
  scheme_name: string;
  scheme_code: string;
  amount: number;
  interest_rate: number;
  investment_date: string;
  start_date: string;
  maturity_date: string;
  maturity_amount: number;
  commission_percentage: number;
  tds_amount: number;
  actual_profit: number;
  status: string;
  lock_in_period_months: number;
  clients: {
    first_name: string;
    last_name: string;
  };
}


interface Transaction {
  id: string;
  investment_id: string;
  amount: number;
  amount_type: string;
  transaction_date: string;
  reference_number: string;
  remark: string;
  investments: {
    scheme_name: string;
    scheme_code: string;
    clients: {
      first_name: string;
      last_name: string;
    };
  };
}



const Reports: React.FC = () => {
  const [activeTab, setActiveTab] = useState('client');
  const [loading, setLoading] = useState(false);
  const [clients, setClients] = useState<Client[]>([]);
  const [investments, setInvestments] = useState<Investment[]>([]);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [schemes, setSchemes] = useState<{ id: string; name: string; scheme_code: string }[]>([]);
  const [allClients, setAllClients] = useState<{ id: string; first_name: string; last_name: string }[]>([]);


  const [filters, setFilters] = useState<ReportFilters>({
    searchTerm: '',
    dateFrom: '',
    dateTo: '',
    status: 'all',
    scheme: 'all',
    client: 'all',
    amountFrom: '',
    amountTo: '',
    transactionType: 'all',
  });

  const [searchInput, setSearchInput] = useState('');


  const [dateFilter, setDateFilter] = useState('all');
  const [customDateRange, setCustomDateRange] = useState<DateRange>();


  useEffect(() => {
    fetchInitialData();
  }, []);


  // Debounced search function
  const debouncedSearch = useCallback(
    debounce((value: string) => {
      setFilters(prev => ({ ...prev, searchTerm: value }));
    }, 500),
    []
  );

  // Handle search input change
  useEffect(() => {
    debouncedSearch(searchInput);
    return () => {
      debouncedSearch.cancel();
    };
  }, [searchInput, debouncedSearch]);

  useEffect(() => {
    fetchReportData();
  }, [activeTab, filters]);


  const fetchInitialData = async () => {
    try {
      const [schemesRes, clientsRes] = await Promise.all([
        supabase.from('schemes').select('id, name, scheme_code').eq('is_active', true),
        supabase.from('clients').select('id, first_name, last_name').eq('is_deleted', false)
      ]);


      if (schemesRes.error) throw schemesRes.error;
      if (clientsRes.error) throw clientsRes.error;


      setSchemes(schemesRes.data || []);
      setAllClients(clientsRes.data || []);
    } catch (error) {
      console.error('Error fetching initial data:', error);
    }
  };


  const fetchReportData = async () => {
    setLoading(true);
    try {
      switch (activeTab) {
        case 'client':
          await fetchClientReport();
          break;
        case 'investment':
          await fetchInvestmentReport();
          break;
        case 'transaction':
          await fetchTransactionReport();
          break;
        case 'commission':
          await fetchCommissionReport();
          break;
        case 'maturity':
          await fetchMaturityReport();
          break;
      }
    } catch (error) {
      console.error('Error fetching report data:', error);
      toast({
        title: "Error",
        description: "Failed to fetch report data",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };


  const fetchClientReport = async () => {
    let query = supabase
      .from('clients')
      .select(`
        id,
        first_name,
        last_name,
        email,
        mobile_number,
        aadhar_number,
        pan_card_number,
        created_at
      `)
      .eq('is_deleted', false);


    if (filters.searchTerm) {
      query = query.or(`first_name.ilike.%${filters.searchTerm}%,last_name.ilike.%${filters.searchTerm}%,email.ilike.%${filters.searchTerm}%`);
    }


    const dateRange = getDateRangeValues(dateFilter, customDateRange, true);
    if (dateRange) {
      query = query.gte('created_at', dateRange.from).lte('created_at', dateRange.to);
    }


    const { data, error } = await query;
    if (error) throw error;


    // Fetch investment totals for each client
    const clientsWithInvestments = await Promise.all(
      (data || []).map(async (client) => {
        const { data: investments } = await supabase
          .from('investments')
          .select('amount')
          .eq('client_id', client.id);

        const totalInvestment = investments?.reduce((sum, inv) => sum + (inv.amount || 0), 0) || 0;

        return {
          ...client,
          total_investment: totalInvestment,
          sb_account_number: 'N/A'
        };
      })
    );

    // Apply client status filter
    let filteredClients = clientsWithInvestments;
    if (filters.status === 'invested') {
      filteredClients = clientsWithInvestments.filter(client => client.total_investment! > 0);
    } else if (filters.status === 'no-invested') {
      filteredClients = clientsWithInvestments.filter(client => client.total_investment === 0);
    }

    setClients(filteredClients);
  };


  const fetchInvestmentReport = async () => {
    let query = supabase
      .from('investments')
      .select(`
        *,
        clients!investments_client_id_fkey(first_name, last_name)
      `);


    if (filters.client !== 'all') {
      query = query.eq('client_id', filters.client);
    }


    if (filters.status !== 'all') {
      query = query.eq('status', filters.status);
    }


    const dateRange = getDateRangeValues(dateFilter, customDateRange, false);
    if (dateRange) {
      query = query.gte('investment_date', dateRange.from).lte('investment_date', dateRange.to);
    }


    if (filters.amountFrom) {
      query = query.gte('amount', parseFloat(filters.amountFrom));
    }


    if (filters.amountTo) {
      query = query.lte('amount', parseFloat(filters.amountTo));
    }


    const { data, error } = await query;
    if (error) throw error;


    setInvestments(data || []);
  };


  const fetchTransactionReport = async () => {
    let query = supabase
      .from('transactions')
      .select(`
        *,
        investments!transactions_investment_id_fkey(
          scheme_name,
          scheme_code,
          clients!investments_client_id_fkey(first_name, last_name)
        )
      `);


    if (filters.transactionType !== 'all') {
      query = query.eq('amount_type', filters.transactionType);
    }


    const dateRange = getDateRangeValues(dateFilter, customDateRange, true);
    if (dateRange) {
      query = query.gte('transaction_date', dateRange.from).lte('transaction_date', dateRange.to);
    }


    const { data, error } = await query;
    if (error) throw error;


    setTransactions(data || []);
  };


  const fetchCommissionReport = async () => {
    await fetchInvestmentReport(); // Use same data as investment report
  };


  const fetchMaturityReport = async () => {
    let query = supabase
      .from('investments')
      .select(`
        *,
        clients!investments_client_id_fkey(first_name, last_name)
      `)
      .eq('status', 'active');


    const dateRange = getDateRangeValues(dateFilter, customDateRange, true);
    if (dateRange) {
      query = query.gte('maturity_date', dateRange.from).lte('maturity_date', dateRange.to);
    }


    const { data, error } = await query;
    if (error) throw error;


    setInvestments(data || []);
  };


  const getFilterSummary = () => {
    const filterDetails = [];

    // Date filter
    let dateFilterText = 'All Time';
    if (dateFilter !== 'all') {
      const dateRange = getDateRangeValues(dateFilter, customDateRange);
      if (dateRange) {
        dateFilterText = `${new Date(dateRange.from).toLocaleDateString()} to ${new Date(dateRange.to).toLocaleDateString()}`;
      } else if (dateFilter === 'custom' && customDateRange) {
        dateFilterText = `${new Date(customDateRange.from).toLocaleDateString()} to ${new Date(customDateRange.to).toLocaleDateString()}`;
      } else {
        const filterLabels = {
          'today': 'Today',
          'yesterday': 'Yesterday',
          'this-week': 'This Week',
          'last-week': 'Last Week',
          'last-7-days': 'Last 7 Days',
          'this-month': 'This Month',
          'last-month': 'Last Month',
          'last-30-days': 'Last 30 Days',
          'this-quarter': 'This Quarter',
          'last-quarter': 'Last Quarter',
          'last-90-days': 'Last 90 Days',
          'last-180-days': 'Last 180 Days',
          'this-year': 'This Year',
          'last-year': 'Last Year'
        };
        dateFilterText = filterLabels[dateFilter] || dateFilter;
      }
    }
    filterDetails.push({ label: 'Date Range', value: dateFilterText, applied: dateFilter !== 'all' });

    // Status filter (context-aware)
    let statusText = 'All Status';
    if (filters.status !== 'all') {
      if (activeTab === 'client') {
        statusText = filters.status === 'invested' ? 'Invested Clients' :
          filters.status === 'no-invested' ? 'No Investment Clients' :
            filters.status.charAt(0).toUpperCase() + filters.status.slice(1);
      } else {
        statusText = filters.status.charAt(0).toUpperCase() + filters.status.slice(1);
      }
    }

    // Only show status filter for investment report
    if (activeTab === 'investment' || activeTab === 'client') {
      filterDetails.push({ label: 'Status', value: statusText, applied: filters.status !== 'all' });
    }

    // Client filter (only for relevant tabs)
    if (activeTab === 'investment' || activeTab === 'transaction' || activeTab === 'commission' || activeTab === 'maturity') {
      let clientText = 'All Clients';
      if (filters.client !== 'all') {
        const selectedClient = allClients.find(c => c.id === filters.client);
        if (selectedClient) {
          clientText = `${selectedClient.first_name} ${selectedClient.last_name}`;
        }
      }
      filterDetails.push({ label: 'Client', value: clientText, applied: filters.client !== 'all' });
    }

    // Transaction type filter (only for transaction tab)
    if (activeTab === 'transaction') {
      const transactionText = filters.transactionType === 'all' ? 'All Types' : filters.transactionType.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
      filterDetails.push({ label: 'Transaction Type', value: transactionText, applied: filters.transactionType !== 'all' });
    }

    // Search term
    if (filters.searchTerm) {
      filterDetails.push({ label: 'Search', value: `"${filters.searchTerm}"`, applied: true });
    }

    return filterDetails;
  };

  const exportToPDF = () => {
    try {
      // Open a new window for the PDF report
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        toast({
          title: "Error",
          description: "Could not open print window. Please check your browser settings.",
          variant: "destructive",
        });
        return;
      }
      // Generate the report content based on active tab
      let title = '';
      let content = '';
      let details = '';
      const filterSummary = getFilterSummary();
      switch (activeTab) {
        case 'client': {
          title = 'Client Report';
          const totalClientsCount = clients.length;
          const investedClientsCount = clients.filter(c => c.total_investment! > 0).length;
          const totalInvestmentValue = clients.reduce((sum, c) => sum + (c.total_investment || 0), 0);
          const noInvestmentClientsCount = clients.filter(c => c.total_investment === 0).length;

          details = clients.map((client, index) => `
            <tr class="detail-item">
              <td>${index + 1}</td>
              <td>${client.first_name} ${client.last_name}</td>
              <td>${client.email || client.mobile_number}</td>
              <td>${client.aadhar_number}</td>
              <td>${client.pan_card_number}</td>
              <td>${client.sb_account_number}</td>
              <td>₹${client.total_investment?.toLocaleString() || '0'}</td>
              <td><span class="status-badge status-active">Active</span></td>
              <td>${new Date(client.created_at).toLocaleDateString()}</td>
            </tr>
          `).join('');

          content = `
            <div class="summary-stats">
              <div class="stat-item">
                <div class="stat-value">${totalClientsCount}</div>
                <div class="stat-label">Total Clients</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">${investedClientsCount}</div>
                <div class="stat-label">Invested Clients</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">₹${totalInvestmentValue.toLocaleString()}</div>
                <div class="stat-label">Total Investment</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">${noInvestmentClientsCount}</div>
                <div class="stat-label">No Investment</div>
              </div>
            </div>
            <table class="report-table">
              <thead>
                <tr>
                  <th>S.No</th>
                  <th>Client Name</th>
                  <th>Email/Phone</th>
                  <th>Aadhar</th>
                  <th>PAN</th>
                  <th>SB Account</th>
                  <th>Total Investment</th>
                  <th>Status</th>
                  <th>Created Date</th>
                </tr>
              </thead>
              <tbody>
                ${details}
              </tbody>
            </table>
          `;
          break;
        }
        case 'investment': {
          title = 'Investment Report';
          const totalInvestmentsCount = investments.length;
          const totalInvestmentAmount = investments.reduce((sum, inv) => sum + inv.amount, 0);
          const totalMaturityAmountValue = investments.reduce((sum, inv) => sum + inv.maturity_amount, 0);
          const activeInvestmentsCount = investments.filter(inv => inv.status === 'active').length;

          details = investments.map((investment, index) => `
            <tr class="detail-item">
              <td>${index + 1}</td>
              <td>${investment.clients ? `${investment.clients.first_name} ${investment.clients.last_name}` : 'N/A'}</td>
              <td>${investment.scheme_name}</td>
              <td>₹${investment.amount.toLocaleString()}</td>
              <td>${investment.interest_rate}%</td>
              <td>${new Date(investment.investment_date).toLocaleDateString()}</td>
              <td>${new Date(investment.maturity_date).toLocaleDateString()}</td>
              <td>₹${investment.maturity_amount.toLocaleString()}</td>
              <td><span class="status-badge status-${investment.status}">${investment.status}</span></td>
            </tr>
          `).join('');

          content = `
            <div class="summary-stats">
              <div class="stat-item">
                <div class="stat-value">${totalInvestmentsCount}</div>
                <div class="stat-label">Total Investments</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">₹${totalInvestmentAmount.toLocaleString()}</div>
                <div class="stat-label">Total Investment</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">₹${totalMaturityAmountValue.toLocaleString()}</div>
                <div class="stat-label">Total Maturity</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">${activeInvestmentsCount}</div>
                <div class="stat-label">Active Investments</div>
              </div>
            </div>
            <table class="report-table">
              <thead>
                <tr>
                  <th>S.No</th>
                  <th>Client Name</th>
                  <th>Scheme Name</th>
                  <th>Amount</th>
                  <th>Interest Rate</th>
                  <th>Investment Date</th>
                  <th>Maturity Date</th>
                  <th>Maturity Amount</th>
                  <th>Status</th>
                </tr>
              </thead>
              <tbody>
                ${details}
              </tbody>
            </table>
          `;
          break;
        }
        case 'transaction': {
          title = 'Transaction Report';
          const totalTransactionsCount = transactions.length;
          const totalTransactionAmountValue = transactions.reduce((sum, t) => sum + t.amount, 0);
          const investmentTransactionsCount = transactions.filter(t => t.amount_type === 'investment').length;
          const payoutTransactionsCount = transactions.filter(t => t.amount_type.includes('payout')).length;

          details = transactions.map((transaction, index) => `
            <tr class="detail-item">
              <td>${index + 1}</td>
              <td>${transaction.investment_id.substring(0, 8)}...</td>
              <td>${transaction.investments?.clients ? `${transaction.investments.clients.first_name} ${transaction.investments.clients.last_name}` : 'N/A'}</td>
              <td>${transaction.investments?.scheme_name || 'N/A'}</td>
              <td>${new Date(transaction.transaction_date).toLocaleDateString()}</td>
              <td>₹${transaction.amount.toLocaleString()}</td>
              <td>${transaction.amount_type}</td>
              <td>${transaction.reference_number}</td>
            </tr>
          `).join('');

          content = `
            <div class="summary-stats">
              <div class="stat-item">
                <div class="stat-value">${totalTransactionsCount}</div>
                <div class="stat-label">Total Transactions</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">₹${totalTransactionAmountValue.toLocaleString()}</div>
                <div class="stat-label">Total Amount</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">${investmentTransactionsCount}</div>
                <div class="stat-label">Investments</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">${payoutTransactionsCount}</div>
                <div class="stat-label">Payouts</div>
              </div>
            </div>
            <table class="report-table">
              <thead>
                <tr>
                  <th>S.No</th>
                  <th>Investment ID</th>
                  <th>Client Name</th>
                  <th>Scheme Name</th>
                  <th>Date</th>
                  <th>Amount</th>
                  <th>Type</th>
                  <th>Reference</th>
                </tr>
              </thead>
              <tbody>
                ${details}
              </tbody>
            </table>
          `;
          break;
        }
        case 'commission': {
          title = 'Commission Report';
          const totalCommissionAmount = investments.reduce((sum, inv) => sum + (inv.amount * inv.commission_percentage) / 100, 0);
          const totalTDSAmount = investments.reduce((sum, inv) => sum + inv.tds_amount, 0);
          const totalNetProfitAmount = investments.reduce((sum, inv) => sum + inv.actual_profit, 0);
          const averageCommissionRate = investments.length > 0 ? investments.reduce((sum, inv) => sum + inv.commission_percentage, 0) / investments.length : 0;

          details = investments.map((investment, index) => {
            const commissionAmount = (investment.amount * investment.commission_percentage) / 100;
            return `
              <tr class="detail-item">
                <td>${index + 1}</td>
                <td>${investment.clients ? `${investment.clients.first_name} ${investment.clients.last_name}` : 'N/A'}</td>
                <td>${investment.scheme_name}</td>
                <td>₹${investment.amount.toLocaleString()}</td>
                <td>${investment.commission_percentage}%</td>
                <td>₹${commissionAmount.toLocaleString()}</td>
                <td>₹${investment.tds_amount.toLocaleString()}</td>
                <td>₹${investment.actual_profit.toLocaleString()}</td>
                <td>${new Date(investment.investment_date).toLocaleDateString()}</td>
              </tr>
            `;
          }).join('');

          content = `
            <div class="summary-stats">
              <div class="stat-item">
                <div class="stat-value">₹${totalCommissionAmount.toLocaleString()}</div>
                <div class="stat-label">Total Commission</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">₹${totalTDSAmount.toLocaleString()}</div>
                <div class="stat-label">Total TDS</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">₹${totalNetProfitAmount.toLocaleString()}</div>
                <div class="stat-label">Net Profit</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">${averageCommissionRate.toFixed(1)}%</div>
                <div class="stat-label">Avg Commission Rate</div>
              </div>
            </div>
            <table class="report-table">
              <thead>
                <tr>
                  <th>S.No</th>
                  <th>Client Name</th>
                  <th>Scheme Name</th>
                  <th>Investment Amount</th>
                  <th>Commission Rate</th>
                  <th>Commission Amount</th>
                  <th>TDS Amount</th>
                  <th>Net Profit</th>
                  <th>Date</th>
                </tr>
              </thead>
              <tbody>
                ${details}
              </tbody>
            </table>
          `;
          break;
        }
        case 'maturity': {
          title = 'Maturity Report';
          const isMaturitySoon = (maturityDate: string) => {
            const maturity = new Date(maturityDate);
            const today = new Date();
            const daysUntilMaturity = Math.ceil((maturity.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
            return daysUntilMaturity <= 30 && daysUntilMaturity > 0;
          };

          const getDaysUntilMaturity = (maturityDate: string) => {
            const maturity = new Date(maturityDate);
            const today = new Date();
            const daysUntilMaturity = Math.ceil((maturity.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
            return daysUntilMaturity;
          };

          const maturingSoonCount = investments.filter(inv => isMaturitySoon(inv.maturity_date)).length;
          const totalMaturityValueAmount = investments.reduce((sum, inv) => sum + inv.maturity_amount, 0);
          const activeInvestmentCount = investments.filter(inv => inv.status === 'active').length;
          const averageLockInPeriod = investments.length > 0 ? investments.reduce((sum, inv) => sum + inv.lock_in_period_months, 0) / investments.length : 0;

          details = investments.map((investment, index) => {
            const daysUntilMaturity = getDaysUntilMaturity(investment.maturity_date);
            const isSoon = isMaturitySoon(investment.maturity_date);
            return `
              <tr class="detail-item ${isSoon ? 'maturity-soon' : ''}">
                <td>${index + 1}</td>
                <td>${investment.clients ? `${investment.clients.first_name} ${investment.clients.last_name}` : 'N/A'}</td>
                <td>${investment.scheme_name}</td>
                <td>₹${investment.amount.toLocaleString()}</td>
                <td>${new Date(investment.maturity_date).toLocaleDateString()}</td>
                <td>₹${investment.maturity_amount.toLocaleString()}</td>
                <td>${daysUntilMaturity > 0 ? `${daysUntilMaturity} days` : 'Matured'}</td>
                <td>${investment.lock_in_period_months} months</td>
                <td><span class="status-badge status-${investment.status}">${investment.status}${isSoon ? ' (Soon)' : ''}</span></td>
              </tr>
            `;
          }).join('');

          content = `
            <div class="summary-stats">
              <div class="stat-item">
                <div class="stat-value">${maturingSoonCount}</div>
                <div class="stat-label">Maturing Soon (30 days)</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">₹${totalMaturityValueAmount.toLocaleString()}</div>
                <div class="stat-label">Total Maturity Value</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">${activeInvestmentCount}</div>
                <div class="stat-label">Active Investments</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">${averageLockInPeriod.toFixed(0)} mo</div>
                <div class="stat-label">Avg Lock-in Period</div>
              </div>
            </div>
            <table class="report-table">
              <thead>
                <tr>
                  <th>S.No</th>
                  <th>Client Name</th>
                  <th>Scheme Name</th>
                  <th>Amount</th>
                  <th>Maturity Date</th>
                  <th>Maturity Amount</th>
                  <th>Days Until Maturity</th>
                  <th>Lock-in Period</th>
                  <th>Status</th>
                </tr>
              </thead>
              <tbody>
                ${details}
              </tbody>
            </table>
          `;
          break;
        }
      }
      // Generate the full HTML document with styling
      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>${title} - ${new Date().toLocaleDateString()}</title>
          <style>
            @page {
              margin: 0.5in 0.75in;
              size: A4;
              @top-left {
                content: "InvestPro Report";
                font-size: 10px;
                color: #666;
                font-weight: 500;
              }
              @top-right {
                content: "Page " counter(page) " of " counter(pages);
                font-size: 10px;
                color: #666;
                font-weight: 500;
              }
              @bottom-center {
                content: "Generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}";
                font-size: 9px;
                color: #999;
              }
            }

            * {
              box-sizing: border-box;
              margin: 0;
              padding: 0;
            }

            body {
              font-family: 'Arial', 'Helvetica', sans-serif;
              color: #333;
              line-height: 1.4;
              background-color: #ffffff;
              font-size: 11px;
            }

            .header {
              display: flex;
              justify-content: space-between;
              align-items: flex-start;
              padding: 20px 0;
              margin-bottom: 20px;
              border-bottom: 2px solid #e0e0e0;
            }

            .header-left {
              display: flex;
              align-items: center;
              gap: 15px;
            }

            .logo {
              display: flex;
              align-items: center;
              
            }

            .logo img {
              max-height: 35px;
              max-width: 120px;
              object-fit: contain;
            }
            

            .header-title {
              color: #1a1a1a;
              font-size: 24px;
              font-weight: 600;
              margin: 0;
            }

            .header-right {
              text-align: right;
              color: #666;
              font-size: 10px;
            }

            .header-right h3 {
              color: #333;
              font-size: 12px;
              font-weight: 600;
              margin-bottom: 5px;
            }

            .report-title {
              color: #4f46e5;
              font-size: 20px;
              font-weight: 600;
              margin: 20px 0 15px 0;
              border-bottom: 1px solid #e0e0e0;
              padding-bottom: 8px;
            }
            
            .filter-info {
              background: #f8f9fa;
              padding: 15px;
              margin-bottom: 20px;
              border-radius: 6px;
              border: 1px solid #e0e0e0;
            }

            .filter-info h3 {
              margin: 0 0 10px 0;
              color: #333;
              font-size: 12px;
              font-weight: 600;
            }

            .filter-grid {
              display: grid;
              grid-template-columns: repeat(2, 1fr);
              gap: 8px;
            }

            .filter-item {
              display: flex;
              justify-content: space-between;
              padding: 6px 10px;
              border-radius: 4px;
              font-size: 10px;
              border: 1px solid #e0e0e0;
              background: white;
            }

            .filter-label {
              font-weight: 600;
              color: #555;
            }

            .filter-value {
              color: #333;
              font-weight: 500;
            }
            
            .summary-stats {
              display: grid;
              grid-template-columns: repeat(4, 1fr);
              gap: 15px;
              margin: 20px 0;
            }

            .stat-item {
              text-align: center;
              padding: 15px 10px;
              background: #f8f9fa;
              border-radius: 6px;
              border: 1px solid #e0e0e0;
            }

            .stat-value {
              font-size: 18px;
              font-weight: 700;
              color: #4f46e5;
              margin-bottom: 4px;
              line-height: 1.2;
            }

            .stat-label {
              font-size: 10px;
              color: #666;
              font-weight: 500;
            }
            
            .report-table {
              width: 100%;
              margin-top: 20px;
              font-size: 10px;
              border-collapse: collapse;
              background: white;
              border: 1px solid #e0e0e0;
            }

            .report-table th,
            .report-table td {
              padding: 8px 10px;
              text-align: left;
              border-bottom: 1px solid #e0e0e0;
              vertical-align: middle;
            }

            .report-table th {
              background: #f8f9fa;
              color: #333;
              font-weight: 600;
              font-size: 10px;
              border-bottom: 2px solid #e0e0e0;
            }

            .report-table tbody tr:nth-child(even) {
              background-color: #f8f9fa;
            }

            .report-table tbody tr:nth-child(odd) {
              background-color: white;
            }

            .report-table tr:last-child td {
              border-bottom: 1px solid #e0e0e0;
            }
            
            .status-badge {
              padding: 3px 8px;
              border-radius: 12px;
              font-size: 9px;
              font-weight: 500;
              white-space: nowrap;
              display: inline-block;
            }

            .status-active {
              background: #e8f5e8;
              color: #2e7d32;
            }

            .status-matured {
              background: #e3f2fd;
              color: #1976d2;
            }

            .status-withdrawn {
              background: #f5f5f5;
              color: #616161;
            }

            .status-reinvested {
              background: #f3e5f5;
              color: #7b1fa2;
            }

            .maturity-soon {
              background: #fff3e0 !important;
              border-left: 3px solid #ff9800;
            }

            .footer {
              margin-top: 30px;
              text-align: center;
              padding: 15px 0;
              border-top: 1px solid #e0e0e0;
            }

            .footer p {
              margin: 3px 0;
              font-size: 9px;
              color: #999;
              line-height: 1.3;
            }
            
            @media print {
              body {
                margin: 0;
                padding: 10px;
              }

              .report-table {
                page-break-inside: auto;
                border: 1px solid #e0e0e0;
              }

              .report-table tr {
                page-break-inside: avoid;
                page-break-after: auto;
              }

              .header {
                page-break-after: avoid;
              }

              .filter-info {
                page-break-after: avoid;
              }

              .summary-stats {
                page-break-after: avoid;
              }

              .filter-grid {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 6px;
              }

              .filter-item {
                padding: 4px 8px;
                font-size: 9px;
              }

              .stat-item {
                border: 1px solid #e0e0e0;
              }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="header-left">
              <div class="logo">
                  <img src="/lovable-uploads/ab651bb9-5504-40da-ab30-1bd5fd7fdc5a.png" alt="InvestPro Logo 1" />
                  <img src="/lovable-uploads/e26f8b82-a2a2-46cc-a26e-fcf66ed47997.png" alt="InvestPro Logo 2" />
              </div>
      
            </div>
            <div class="header-right">
              <h3>InvestPro Investment Management</h3>
              <p>Generated on ${new Date().toLocaleDateString()}</p>
              <p>Time: ${new Date().toLocaleTimeString()}</p>
            </div>
          </div>

          <h2 class="report-title">${title}</h2>

          <div class="filter-info">
            <h3>Report Filters</h3>
            <div class="filter-grid">
              ${getFilterSummary().map(filter => `
                <div class="filter-item">
                  <span class="filter-label">${filter.label}:</span>
                  <span class="filter-value">${filter.value}</span>
                </div>
              `).join('')}
            </div>
          </div>

          ${content}

          <div class="footer">
            <p>This report was generated by InvestPro Investment Management System</p>
            <p>For any queries, please contact your system administrator</p>
            <p>© ${new Date().getFullYear()} InvestPro. All rights reserved.</p>
          </div>
        </body>
        </html>
      `;
      // Write the content to the new window
      printWindow.document.write(htmlContent);
      printWindow.document.close();
      printWindow.focus();
      // Wait for content to load then print
      setTimeout(() => {
        printWindow.print();
        printWindow.close();
      }, 250);
      toast({
        title: "Report Generated",
        description: "Report has been generated. Please use your browser's print dialog to save as PDF.",
      });
    } catch (error) {
      console.error('Error generating PDF:', error);
      toast({
        title: "Error",
        description: "Failed to generate PDF report",
        variant: "destructive",
      });
    }
  };



  const exportToExcel = () => {
    try {
      let csvContent = '';
      let headers = '';


      switch (activeTab) {
        case 'client':
          headers = 'Client Name,Email/Phone,Aadhar,PAN,SB Account,Total Investment,Status,Created Date\n';
          csvContent = clients.map(client =>
            `"${client.first_name} ${client.last_name}","${client.email || client.mobile_number}","${client.aadhar_number}","${client.pan_card_number}","${client.sb_account_number}","₹${client.total_investment?.toLocaleString() || '0'}","Active","${new Date(client.created_at).toLocaleDateString()}"`
          ).join('\n');
          break;
        case 'investment':
          headers = 'Client Name,Scheme Name,Invested Amount,Interest Rate,Investment Date,Maturity Date,Maturity Amount,Status\n';
          csvContent = investments.map(investment =>
            `"${investment.clients ? `${investment.clients.first_name} ${investment.clients.last_name}` : 'N/A'}","${investment.scheme_name}","₹${investment.amount.toLocaleString()}","${investment.interest_rate}%","${new Date(investment.investment_date).toLocaleDateString()}","${new Date(investment.maturity_date).toLocaleDateString()}","₹${investment.maturity_amount.toLocaleString()}","${investment.status}"`
          ).join('\n');
          break;
        case 'transaction':
          headers = 'Investment ID,Client Name,Scheme,Transaction Date,Amount,Type,Reference\n';
          csvContent = transactions.map(transaction =>
            `"${transaction.investment_id.substring(0, 8)}...","${transaction.investments?.clients ? `${transaction.investments.clients.first_name} ${transaction.investments.clients.last_name}` : 'N/A'}","${transaction.investments?.scheme_name || 'N/A'}","${new Date(transaction.transaction_date).toLocaleDateString()}","₹${transaction.amount.toLocaleString()}","${transaction.amount_type}","${transaction.reference_number}"`
          ).join('\n');
          break;
        case 'commission':
          headers = 'Client,Scheme,Investment Amount,Commission Amount,TDS,Net Profit,Investment Date\n';
          csvContent = investments.map(investment => {
            const commissionAmount = (investment.amount * investment.commission_percentage) / 100;
            return `"${investment.clients ? `${investment.clients.first_name} ${investment.clients.last_name}` : 'N/A'}","${investment.scheme_name}","₹${investment.amount.toLocaleString()}","₹${commissionAmount.toLocaleString()}","₹${investment.tds_amount.toLocaleString()}","₹${investment.actual_profit.toLocaleString()}","${new Date(investment.investment_date).toLocaleDateString()}"`;
          }).join('\n');
          break;
        case 'maturity':
          headers = 'Client,Scheme,Invested Amount,Maturity Date,Maturity Amount,Lock-in Period,Status\n';
          csvContent = investments.map(investment =>
            `"${investment.clients ? `${investment.clients.first_name} ${investment.clients.last_name}` : 'N/A'}","${investment.scheme_name}","₹${investment.amount.toLocaleString()}","${new Date(investment.maturity_date).toLocaleDateString()}","₹${investment.maturity_amount.toLocaleString()}","${investment.lock_in_period_months} months","${investment.status}"`
          ).join('\n');
          break;
      }


      const element = document.createElement('a');
      const file = new Blob([headers + csvContent], { type: 'text/csv' });
      element.href = URL.createObjectURL(file);
      element.download = `${activeTab}_report_${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(element);
      element.click();
      document.body.removeChild(element);

      toast({
        title: "Success",
        description: "Excel report exported successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to export Excel report",
        variant: "destructive",
      });
    }
  };


  const resetFilters = () => {
    setFilters({
      searchTerm: '',
      dateFrom: '',
      dateTo: '',
      status: 'all',
      scheme: 'all',
      client: 'all',
      amountFrom: '',
      amountTo: '',
      transactionType: 'all',
    });
    setDateFilter('all');
    setCustomDateRange(undefined);
  };


  const getStatusBadge = (status: string, type: 'client' | 'investment' = 'investment') => {
    if (type === 'client') {
      return status === 'invested' ? (
        <Badge variant="default" className="bg-green-100 text-green-800">Invested</Badge>
      ) : (
        <Badge variant="secondary">No Investment</Badge>
      );
    }

    const variants = {
      active: <Badge variant="default" className="bg-green-100 text-green-800">Active</Badge>,
      matured: <Badge variant="default" className="bg-blue-100 text-blue-800">Matured</Badge>,
      withdrawn: <Badge variant="secondary">Withdrawn</Badge>,
      reinvested: <Badge variant="default" className="bg-purple-100 text-purple-800">Reinvested</Badge>
    };
    return variants[status as keyof typeof variants] || <Badge variant="secondary">{status}</Badge>;
  };

  const renderClientReport = () => (
    <div className="space-y-4">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Users className="h-8 w-8 text-blue-600" />
              <div>
                <p className="text-2xl font-bold">{clients.length}</p>
                <p className="text-sm text-gray-600">Total Clients</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-8 w-8 text-green-600" />
              <div>
                <p className="text-2xl font-bold">{clients.filter(c => c.total_investment! > 0).length}</p>
                <p className="text-sm text-gray-600">Invested Clients</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-8 w-8 text-yellow-600" />
              <div>
                <p className="text-2xl font-bold">₹{clients.reduce((sum, c) => sum + (c.total_investment || 0), 0).toLocaleString()}</p>
                <p className="text-sm text-gray-600">Total Investment</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Calendar className="h-8 w-8 text-purple-600" />
              <div>
                <p className="text-2xl font-bold">{clients.filter(c => c.total_investment === 0).length}</p>
                <p className="text-sm text-gray-600">No Investment</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Data Table */}
      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow className="bg-gray-50">
              <TableHead className="font-semibold w-16">S.No</TableHead>
              <TableHead className="font-semibold">Client Name</TableHead>
              <TableHead className="font-semibold">Contact</TableHead>
              <TableHead className="font-semibold">Aadhar Number</TableHead>
              <TableHead className="font-semibold">PAN Card</TableHead>
              <TableHead className="font-semibold text-right">Total Investment</TableHead>
              <TableHead className="font-semibold">Status</TableHead>
              <TableHead className="font-semibold">Join Date</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {clients.map((client: Client, index: number) => (
              <TableRow key={client.id} className="hover:bg-gray-50">
                <TableCell className="font-medium text-center">
                  <span className="text-sm font-bold text-blue-600">{index + 1}</span>
                </TableCell>
                <TableCell className="font-medium">
                  <div>
                    <p className="font-semibold">{client.first_name} {client.last_name}</p>
                    <p className="text-sm text-gray-500">{client.email}</p>
                  </div>
                </TableCell>
                <TableCell>{client.mobile_number || 'N/A'}</TableCell>
                <TableCell>
                  <span className="font-mono text-sm">{client.aadhar_number || 'N/A'}</span>
                </TableCell>
                <TableCell>
                  <span className="font-mono text-sm">{client.pan_card_number || 'N/A'}</span>
                </TableCell>
                <TableCell className="text-right">
                  <span className="font-bold text-green-600">₹{client.total_investment?.toLocaleString() || '0'}</span>
                </TableCell>
                <TableCell>
                  {getStatusBadge(client.total_investment! > 0 ? 'invested' : 'no-investment', 'client')}
                </TableCell>
                <TableCell>
                  <span className="text-sm">{new Date(client.created_at).toLocaleDateString()}</span>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );


  const renderInvestmentReport = () => {
    const totalInvestment = investments.reduce((sum, inv) => sum + inv.amount, 0);
    const totalMaturityAmount = investments.reduce((sum, inv) => sum + inv.maturity_amount, 0);
    const totalProfit = investments.reduce((sum, inv) => sum + inv.actual_profit, 0);
    const activeInvestments = investments.filter(inv => inv.status === 'active').length;

    return (
      <div className="space-y-4">
        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <TrendingUp className="h-8 w-8 text-blue-600" />
                <div>
                  <p className="text-2xl font-bold">{investments.length}</p>
                  <p className="text-sm text-gray-600">Total Investments</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <DollarSign className="h-8 w-8 text-green-600" />
                <div>
                  <p className="text-2xl font-bold">₹{totalInvestment.toLocaleString()}</p>
                  <p className="text-sm text-gray-600">Total Investment</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <TrendingUp className="h-8 w-8 text-purple-600" />
                <div>
                  <p className="text-2xl font-bold">₹{totalMaturityAmount.toLocaleString()}</p>
                  <p className="text-sm text-gray-600">Total Maturity</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <Calendar className="h-8 w-8 text-orange-600" />
                <div>
                  <p className="text-2xl font-bold">{activeInvestments}</p>
                  <p className="text-sm text-gray-600">Active Investments</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Data Table */}
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50">
                <TableHead className="font-semibold w-16">S.No</TableHead>
                <TableHead className="font-semibold">Client Name</TableHead>
                <TableHead className="font-semibold">Scheme</TableHead>
                <TableHead className="font-semibold text-right">Investment Amount</TableHead>
                <TableHead className="font-semibold text-center">Interest Rate</TableHead>
                <TableHead className="font-semibold text-right">Maturity Amount</TableHead>
                <TableHead className="font-semibold text-right">Actual Profit</TableHead>
                <TableHead className="font-semibold">Investment Date</TableHead>
                <TableHead className="font-semibold">Maturity Date</TableHead>
                <TableHead className="font-semibold">Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {investments.map((investment, index: number) => (
                <TableRow key={investment.id} className="hover:bg-gray-50">
                  <TableCell className="font-medium text-center">
                    <span className="text-sm font-bold text-blue-600">{index + 1}</span>
                  </TableCell>
                  <TableCell className="font-medium">
                    <div>
                      <p className="font-semibold">
                        {investment.clients ? `${investment.clients.first_name} ${investment.clients.last_name}` : 'N/A'}
                      </p>
                      <p className="text-sm text-gray-500">{investment.scheme_code}</p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <p className="font-medium">{investment.scheme_name}</p>
                      <p className="text-sm text-gray-500">{investment.lock_in_period_months} months lock-in</p>
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <span className="font-bold text-blue-600">₹{investment.amount.toLocaleString()}</span>
                  </TableCell>
                  <TableCell className="text-center">
                    <Badge variant="outline" className="font-semibold">{investment.interest_rate}%</Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <span className="font-bold text-green-600">₹{investment.maturity_amount.toLocaleString()}</span>
                  </TableCell>
                  <TableCell className="text-right">
                    <span className="font-bold text-orange-600">₹{investment.actual_profit.toLocaleString()}</span>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm">{new Date(investment.investment_date).toLocaleDateString()}</span>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm">{new Date(investment.maturity_date).toLocaleDateString()}</span>
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(investment.status)}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    );
  };


  const renderTransactionReport = () => {
    const totalTransactionAmount = transactions.reduce((sum, t) => sum + t.amount, 0);
    const investmentTransactions = transactions.filter(t => t.amount_type === 'investment').length;
    const payoutTransactions = transactions.filter(t => t.amount_type.includes('payout')).length;
    const otherTransactions = transactions.filter(t => !['investment', 'interest_payout', 'maturity_payout'].includes(t.amount_type)).length;

    const getTransactionTypeBadge = (type: string) => {
      const typeMap = {
        investment: <Badge className="bg-blue-100 text-blue-800">Investment</Badge>,
        interest_payout: <Badge className="bg-green-100 text-green-800">Interest Payout</Badge>,
        maturity_payout: <Badge className="bg-purple-100 text-purple-800">Maturity Payout</Badge>,
        penalty: <Badge variant="destructive">Penalty</Badge>,
        other: <Badge variant="secondary">Other</Badge>
      };
      return typeMap[type as keyof typeof typeMap] || <Badge variant="outline">{type}</Badge>;
    };

    return (
      <div className="space-y-4">
        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <TrendingUp className="h-8 w-8 text-blue-600" />
                <div>
                  <p className="text-2xl font-bold">{transactions.length}</p>
                  <p className="text-sm text-gray-600">Total Transactions</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <DollarSign className="h-8 w-8 text-green-600" />
                <div>
                  <p className="text-2xl font-bold">₹{totalTransactionAmount.toLocaleString()}</p>
                  <p className="text-sm text-gray-600">Total Amount</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <Users className="h-8 w-8 text-purple-600" />
                <div>
                  <p className="text-2xl font-bold">{investmentTransactions}</p>
                  <p className="text-sm text-gray-600">Investments</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <Calendar className="h-8 w-8 text-orange-600" />
                <div>
                  <p className="text-2xl font-bold">{payoutTransactions}</p>
                  <p className="text-sm text-gray-600">Payouts</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Data Table */}
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50">
                <TableHead className="font-semibold w-16">S.No</TableHead>
                <TableHead className="font-semibold">Transaction ID</TableHead>
                <TableHead className="font-semibold">Client Name</TableHead>
                <TableHead className="font-semibold">Scheme</TableHead>
                <TableHead className="font-semibold">Transaction Date</TableHead>
                <TableHead className="font-semibold text-right">Amount</TableHead>
                <TableHead className="font-semibold">Type</TableHead>
                <TableHead className="font-semibold">Reference</TableHead>
                <TableHead className="font-semibold">Remark</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {transactions.map((transaction, index: number) => (
                <TableRow key={transaction.id} className="hover:bg-gray-50">
                  <TableCell className="font-medium text-center">
                    <span className="text-sm font-bold text-blue-600">{index + 1}</span>
                  </TableCell>
                  <TableCell className="font-medium">
                    <span className="font-mono text-sm">{transaction.id.substring(0, 8)}...</span>
                  </TableCell>
                  <TableCell>
                    <div>
                      <p className="font-semibold">
                        {transaction.investments?.clients ?
                          `${transaction.investments.clients.first_name} ${transaction.investments.clients.last_name}` : 'N/A'}
                      </p>
                      <p className="text-sm text-gray-500">{transaction.investment_id.substring(0, 8)}...</p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <p className="font-medium">{transaction.investments?.scheme_name || 'N/A'}</p>
                      <p className="text-sm text-gray-500">{transaction.investments?.scheme_code || 'N/A'}</p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm">{new Date(transaction.transaction_date).toLocaleDateString()}</span>
                  </TableCell>
                  <TableCell className="text-right">
                    <span className="font-bold text-green-600">₹{transaction.amount.toLocaleString()}</span>
                  </TableCell>
                  <TableCell>
                    {getTransactionTypeBadge(transaction.amount_type)}
                  </TableCell>
                  <TableCell>
                    <span className="font-mono text-sm">{transaction.reference_number}</span>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm">{transaction.remark || '-'}</span>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    );
  };


  const renderCommissionReport = () => {
    const totalCommission = investments.reduce((sum, inv) => sum + (inv.amount * inv.commission_percentage) / 100, 0);
    const totalTDS = investments.reduce((sum, inv) => sum + inv.tds_amount, 0);
    const totalNetProfit = investments.reduce((sum, inv) => sum + inv.actual_profit, 0);
    const averageCommissionRate = investments.length > 0 ? investments.reduce((sum, inv) => sum + inv.commission_percentage, 0) / investments.length : 0;

    return (
      <div className="space-y-4">
        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <DollarSign className="h-8 w-8 text-green-600" />
                <div>
                  <p className="text-2xl font-bold">₹{totalCommission.toLocaleString()}</p>
                  <p className="text-sm text-gray-600">Total Commission</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <TrendingUp className="h-8 w-8 text-red-600" />
                <div>
                  <p className="text-2xl font-bold">₹{totalTDS.toLocaleString()}</p>
                  <p className="text-sm text-gray-600">Total TDS</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <Users className="h-8 w-8 text-purple-600" />
                <div>
                  <p className="text-2xl font-bold">₹{totalNetProfit.toLocaleString()}</p>
                  <p className="text-sm text-gray-600">Net Profit</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <Calendar className="h-8 w-8 text-blue-600" />
                <div>
                  <p className="text-2xl font-bold">{averageCommissionRate.toFixed(1)}%</p>
                  <p className="text-sm text-gray-600">Avg Commission</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Data Table */}
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50">
                <TableHead className="font-semibold w-16">S.No</TableHead>
                <TableHead className="font-semibold">Client Name</TableHead>
                <TableHead className="font-semibold">Scheme</TableHead>
                <TableHead className="font-semibold text-right">Investment Amount</TableHead>
                <TableHead className="font-semibold text-center">Commission Rate</TableHead>
                <TableHead className="font-semibold text-right">Commission Amount</TableHead>
                <TableHead className="font-semibold text-right">TDS Amount</TableHead>
                <TableHead className="font-semibold text-right">Net Profit</TableHead>
                <TableHead className="font-semibold">Investment Date</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {investments.map((investment, index: number) => {
                const commissionAmount = (investment.amount * investment.commission_percentage) / 100;
                return (
                  <TableRow key={investment.id} className="hover:bg-gray-50">
                    <TableCell className="font-medium text-center">
                      <span className="text-sm font-bold text-blue-600">{index + 1}</span>
                    </TableCell>
                    <TableCell className="font-medium">
                      <div>
                        <p className="font-semibold">
                          {investment.clients ? `${investment.clients.first_name} ${investment.clients.last_name}` : 'N/A'}
                        </p>
                        <p className="text-sm text-gray-500">{investment.scheme_code}</p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <p className="font-medium">{investment.scheme_name}</p>
                        <p className="text-sm text-gray-500">{investment.lock_in_period_months} months</p>
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <span className="font-bold text-blue-600">₹{investment.amount.toLocaleString()}</span>
                    </TableCell>
                    <TableCell className="text-center">
                      <Badge variant="outline" className="font-semibold text-yellow-700">{investment.commission_percentage}%</Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <span className="font-bold text-green-600">₹{commissionAmount.toLocaleString()}</span>
                    </TableCell>
                    <TableCell className="text-right">
                      <span className="font-bold text-red-600">₹{investment.tds_amount.toLocaleString()}</span>
                    </TableCell>
                    <TableCell className="text-right">
                      <span className="font-bold text-purple-600">₹{investment.actual_profit.toLocaleString()}</span>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm">{new Date(investment.investment_date).toLocaleDateString()}</span>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </div>
      </div>
    );
  };


  const renderMaturityReport = () => {
    const isMaturitySoon = (maturityDate: string) => {
      const maturity = new Date(maturityDate);
      const today = new Date();
      const daysUntilMaturity = Math.ceil((maturity.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
      return daysUntilMaturity <= 30 && daysUntilMaturity > 0;
    };

    const getDaysUntilMaturity = (maturityDate: string) => {
      const maturity = new Date(maturityDate);
      const today = new Date();
      const daysUntilMaturity = Math.ceil((maturity.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
      return daysUntilMaturity;
    };

    const maturingSoon = investments.filter(inv => isMaturitySoon(inv.maturity_date)).length;
    const totalMaturityValue = investments.reduce((sum, inv) => sum + inv.maturity_amount, 0);
    const activeInvestments = investments.filter(inv => inv.status === 'active').length;
    const averageLockInPeriod = investments.length > 0 ? investments.reduce((sum, inv) => sum + inv.lock_in_period_months, 0) / investments.length : 0;

    return (
      <div className="space-y-4">
        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <Calendar className="h-8 w-8 text-orange-600" />
                <div>
                  <p className="text-2xl font-bold">{maturingSoon}</p>
                  <p className="text-sm text-gray-600">Maturing Soon</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <DollarSign className="h-8 w-8 text-green-600" />
                <div>
                  <p className="text-2xl font-bold">₹{totalMaturityValue.toLocaleString()}</p>
                  <p className="text-sm text-gray-600">Total Maturity Value</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <TrendingUp className="h-8 w-8 text-blue-600" />
                <div>
                  <p className="text-2xl font-bold">{activeInvestments}</p>
                  <p className="text-sm text-gray-600">Active Investments</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <Users className="h-8 w-8 text-purple-600" />
                <div>
                  <p className="text-2xl font-bold">{averageLockInPeriod.toFixed(0)} mo</p>
                  <p className="text-sm text-gray-600">Avg Lock-in Period</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Data Table */}
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50">
                <TableHead className="font-semibold w-16">S.No</TableHead>
                <TableHead className="font-semibold">Client Name</TableHead>
                <TableHead className="font-semibold">Scheme</TableHead>
                <TableHead className="font-semibold text-right">Investment Amount</TableHead>
                <TableHead className="font-semibold text-right">Maturity Amount</TableHead>
                <TableHead className="font-semibold">Maturity Date</TableHead>
                <TableHead className="font-semibold text-center">Days Until Maturity</TableHead>
                <TableHead className="font-semibold text-center">Lock-in Period</TableHead>
                <TableHead className="font-semibold">Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {investments.map((investment, index: number) => {
                const daysUntilMaturity = getDaysUntilMaturity(investment.maturity_date);
                const isSoon = isMaturitySoon(investment.maturity_date);

                return (
                  <TableRow key={investment.id} className={`hover:bg-gray-50 ${isSoon ? 'bg-orange-50 border-l-4 border-orange-400' : ''}`}>
                    <TableCell className="font-medium text-center">
                      <span className="text-sm font-bold text-blue-600">{index + 1}</span>
                    </TableCell>
                    <TableCell className="font-medium">
                      <div>
                        <p className="font-semibold">
                          {investment.clients ? `${investment.clients.first_name} ${investment.clients.last_name}` : 'N/A'}
                        </p>
                        <p className="text-sm text-gray-500">{investment.scheme_code}</p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <p className="font-medium">{investment.scheme_name}</p>
                        <p className="text-sm text-gray-500">{investment.interest_rate}% interest</p>
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <span className="font-bold text-blue-600">₹{investment.amount.toLocaleString()}</span>
                    </TableCell>
                    <TableCell className="text-right">
                      <span className="font-bold text-green-600">₹{investment.maturity_amount.toLocaleString()}</span>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm">{new Date(investment.maturity_date).toLocaleDateString()}</span>
                    </TableCell>
                    <TableCell className="text-center">
                      {daysUntilMaturity > 0 ? (
                        <Badge variant={isSoon ? "destructive" : "outline"} className={isSoon ? "bg-orange-100 text-orange-800" : ""}>
                          {daysUntilMaturity} days
                        </Badge>
                      ) : (
                        <Badge variant="secondary">Matured</Badge>
                      )}
                    </TableCell>
                    <TableCell className="text-center">
                      <Badge variant="outline">{investment.lock_in_period_months} months</Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-1">
                        {isSoon && (
                          <Badge variant="outline" className="bg-orange-100 text-orange-800 text-xs">
                            Soon
                          </Badge>
                        )}
                        {getStatusBadge(investment.status)}
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </div>
      </div>
    );
  };


  return (
    <div className="min-h-screen bg-gray-50/50">
      <div className=" space-y-6">
        {/* Header Section */}
        <div className="bg-white rounded-lg border shadow-sm p-6">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Reports & Analytics</h1>
              <p className="text-gray-600 mt-1">Comprehensive investment and client reporting dashboard</p>
            </div>
            <div className="flex gap-2">
              <Button onClick={exportToPDF} variant="outline" className="flex items-center gap-2">
                <Download className="h-4 w-4" />
                Export PDF
              </Button>
              <Button onClick={exportToExcel} variant="outline" className="flex items-center gap-2">
                <FileSpreadsheet className="h-4 w-4" />
                Export Excel
              </Button>
            </div>
          </div>
        </div>


        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Report Filters
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              <div className="w-full">
                <Label>Search</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search..."
                    value={searchInput}
                    onChange={(e) => setSearchInput(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              {/* Status filter - only for Client and Investment reports */}
              {(activeTab === 'client' || activeTab === 'investment') && (
                <div className="w-full">
                  <Label>Status</Label>
                  <Select value={filters.status} onValueChange={(value) => setFilters({ ...filters, status: value })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {activeTab === 'client' ? (
                        <>
                          <SelectItem value="all">All Clients</SelectItem>
                          <SelectItem value="invested">Invested Clients</SelectItem>
                          <SelectItem value="no-invested">No Investment</SelectItem>
                        </>
                      ) : (
                        <>
                          <SelectItem value="all">All Status</SelectItem>
                          <SelectItem value="active">Active</SelectItem>
                          <SelectItem value="matured">Matured</SelectItem>
                          <SelectItem value="reinvested">Reinvested</SelectItem>
                        </>
                      )}
                    </SelectContent>
                  </Select>
                </div>
              )}
              {/* Client filter - for all reports except client report */}
              {(activeTab === 'investment' || activeTab === 'transaction' || activeTab === 'commission' || activeTab === 'maturity') && (
                <div className="w-full">
                  <Label>Client</Label>
                  <Select value={filters.client} onValueChange={(value) => setFilters({ ...filters, client: value })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Clients</SelectItem>
                      {allClients.map((client) => (
                        <SelectItem key={client.id} value={client.id}>
                          {client.first_name} {client.last_name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}
              {activeTab === 'transaction' && (
                <div className="w-full">
                  <Label>Transaction Type</Label>
                  <Select value={filters.transactionType} onValueChange={(value) => setFilters({ ...filters, transactionType: value })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="investment">Investment</SelectItem>
                      <SelectItem value="interest_payout">Interest Payout</SelectItem>
                      <SelectItem value="maturity_payout">Maturity Payout</SelectItem>
                      <SelectItem value="penalty">Penalty</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}
              <div className="w-full lg:col-span-2">
                <Label>Date Filter</Label>
                <EnhancedDateFilter
                  value={dateFilter}
                  onChange={setDateFilter}
                  customRange={customDateRange}
                  onCustomRangeChange={setCustomDateRange}
                />
              </div>
            </div>
            <div className="flex flex-wrap gap-2 mt-2">
              <Button onClick={fetchReportData} disabled={loading}>
                {loading ? 'Loading...' : 'Apply Filters'}
              </Button>
              <Button onClick={resetFilters} variant="outline">
                Reset Filters
              </Button>
            </div>
          </CardContent>
        </Card>


        {/* Report Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <div className="w-full overflow-x-auto">
            <TabsList className="inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground min-w-max">
              <TabsTrigger value="client" className="whitespace-nowrap px-3 py-1.5 text-sm">Client Report</TabsTrigger>
              <TabsTrigger value="investment" className="whitespace-nowrap px-3 py-1.5 text-sm">Investment Report</TabsTrigger>
              <TabsTrigger value="transaction" className="whitespace-nowrap px-3 py-1.5 text-sm">Transaction Report</TabsTrigger>
              <TabsTrigger value="commission" className="whitespace-nowrap px-3 py-1.5 text-sm">Commission Report</TabsTrigger>
              <TabsTrigger value="maturity" className="whitespace-nowrap px-3 py-1.5 text-sm">Maturity Report</TabsTrigger>
            </TabsList>
          </div>


          <TabsContent value="client" className="space-y-0">
            <div className="bg-white rounded-lg border shadow-sm">
              <div className="border-b px-6 py-4">
                <h3 className="text-lg font-semibold text-gray-900">Client Report</h3>
                <p className="text-sm text-gray-600">{clients.length} records found</p>
              </div>
              <div className="p-6">
                {loading ? (
                  <div className="text-center py-12">
                    <div className="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-white bg-blue-500 hover:bg-blue-400 transition ease-in-out duration-150 cursor-not-allowed">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Loading reports...
                    </div>
                  </div>
                ) : (
                  renderClientReport()
                )}
              </div>
            </div>
          </TabsContent>


          <TabsContent value="investment" className="space-y-0">
            <div className="bg-white rounded-lg border shadow-sm">
              <div className="border-b px-6 py-4">
                <h3 className="text-lg font-semibold text-gray-900">Investment Report</h3>
                <p className="text-sm text-gray-600">{investments.length} records found</p>
              </div>
              <div className="p-6">
                {loading ? (
                  <div className="text-center py-12">
                    <div className="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-white bg-blue-500 hover:bg-blue-400 transition ease-in-out duration-150 cursor-not-allowed">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Loading reports...
                    </div>
                  </div>
                ) : (
                  renderInvestmentReport()
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="transaction" className="space-y-0">
            <div className="bg-white rounded-lg border shadow-sm">
              <div className="border-b px-6 py-4">
                <h3 className="text-lg font-semibold text-gray-900">Transaction Report</h3>
                <p className="text-sm text-gray-600">{transactions.length} records found</p>
              </div>
              <div className="p-6">
                {loading ? (
                  <div className="text-center py-12">
                    <div className="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-white bg-blue-500 hover:bg-blue-400 transition ease-in-out duration-150 cursor-not-allowed">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Loading reports...
                    </div>
                  </div>
                ) : (
                  renderTransactionReport()
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="commission" className="space-y-0">
            <div className="bg-white rounded-lg border shadow-sm">
              <div className="border-b px-6 py-4">
                <h3 className="text-lg font-semibold text-gray-900">Commission Report</h3>
                <p className="text-sm text-gray-600">{investments.length} records found</p>
              </div>
              <div className="p-6">
                {loading ? (
                  <div className="text-center py-12">
                    <div className="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-white bg-blue-500 hover:bg-blue-400 transition ease-in-out duration-150 cursor-not-allowed">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Loading reports...
                    </div>
                  </div>
                ) : (
                  renderCommissionReport()
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="maturity" className="space-y-0">
            <div className="bg-white rounded-lg border shadow-sm">
              <div className="border-b px-6 py-4">
                <h3 className="text-lg font-semibold text-gray-900">Maturity Report</h3>
                <p className="text-sm text-gray-600">{investments.length} records found</p>
              </div>
              <div className="p-6">
                {loading ? (
                  <div className="text-center py-12">
                    <div className="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-white bg-blue-500 hover:bg-blue-400 transition ease-in-out duration-150 cursor-not-allowed">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Loading reports...
                    </div>
                  </div>
                ) : (
                  renderMaturityReport()
                )}
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};


export default Reports;



