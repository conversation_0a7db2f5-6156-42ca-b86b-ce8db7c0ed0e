import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';

import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Plus, Edit, Eye, Trash2, Search, TrendingUp, Clock, DollarSign, Percent, ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import { EnhancedDateFilter, DateRange, getDateRangeValues } from '@/components/ui/enhanced-date-filter';

interface Scheme {
  id: string;
  name: string;
  scheme_code: string;
  interest_rate: number;
  interest_type: string;
  tenure_months: number;
  min_amount: number;
  max_amount: number;
  supports_sip: boolean;
  is_active: boolean;
  payout_type: string;
  compounding_frequency: string;
  lock_in_period_months: number;
  commission_percentage: number;
  min_sip_amount: number;
  max_sip_amount: number;
  created_at: string;
}

const Schemes: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const [schemes, setSchemes] = useState<Scheme[]>([]);
  const [filteredSchemes, setFilteredSchemes] = useState<Scheme[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [itemsPerPage] = useState(9);
  const [dateFilter, setDateFilter] = useState('all');
  const [customDateRange, setCustomDateRange] = useState<DateRange>();

  // Get current page from URL, ensuring it's a valid number
  const getCurrentPage = () => {
    const pageParam = searchParams.get('page');
    const page = pageParam ? parseInt(pageParam, 10) : 1;
    return page > 0 ? page : 1;
  };

  const currentPage = getCurrentPage();
  const [sortField, setSortField] = useState<keyof Scheme>('created_at');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    inactive: 0,
    sipSupported: 0
  });

  useEffect(() => {
    fetchSchemes();
  }, [dateFilter, customDateRange]);

  useEffect(() => {
    applyFilters();
  }, [schemes, searchTerm, statusFilter, typeFilter, sortField, sortDirection]);

  const fetchSchemes = async () => {
    try {
      let query = supabase
        .from('schemes')
        .select('*')
        .order('created_at', { ascending: false });
      // Enhanced date filter
      const dateRange = getDateRangeValues(dateFilter, customDateRange, true);
      if (dateRange) {
        query = query.gte('created_at', dateRange.from).lte('created_at', dateRange.to);
      }
      const { data, error } = await query;

      if (error) throw error;
      setSchemes(data || []);

      // Calculate stats
      const stats = {
        total: (data || []).length,
        active: (data || []).filter(s => s.is_active).length,
        inactive: (data || []).filter(s => !s.is_active).length,
        sipSupported: (data || []).filter(s => s.supports_sip).length
      };
      setStats(stats);
    } catch (error) {
      console.error('Error fetching schemes:', error);
      toast({
        title: "Error",
        description: "Failed to fetch schemes",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const toggleSchemeStatus = async (id: string, currentStatus: boolean) => {
    try {
      const { error } = await supabase
        .from('schemes')
        .update({ is_active: !currentStatus })
        .eq('id', id);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Scheme status updated successfully",
      });

      fetchSchemes();
    } catch (error) {
      console.error('Error updating scheme:', error);
      toast({
        title: "Error",
        description: "Failed to update scheme",
        variant: "destructive",
      });
    }
  };

  const handleSort = (field: keyof Scheme) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const applyFilters = () => {
    let filtered = schemes;

    // Apply search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase().trim();
      filtered = filtered.filter(scheme => {
        const schemeName = scheme.name.toLowerCase();
        const schemeCode = scheme.scheme_code.toLowerCase();

        return schemeName.includes(searchLower) ||
          schemeCode.includes(searchLower);
      });
    }

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(scheme =>
        statusFilter === 'active' ? scheme.is_active : !scheme.is_active
      );
    }

    // Apply type filter
    if (typeFilter !== 'all') {
      if (typeFilter === 'sip') {
        filtered = filtered.filter(scheme => scheme.supports_sip);
      } else {
        filtered = filtered.filter(scheme => scheme.interest_type === typeFilter);
      }
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue = a[sortField];
      let bValue = b[sortField];

      if (typeof aValue === 'string') aValue = aValue.toLowerCase();
      if (typeof bValue === 'string') bValue = bValue.toLowerCase();

      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });

    setFilteredSchemes(filtered);
  };

  const deleteScheme = async (id: string) => {
    try {
      const { error } = await supabase
        .from('schemes')
        .delete()
        .eq('id', id);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Scheme deleted successfully",
      });

      fetchSchemes();
    } catch (error) {
      console.error('Error deleting scheme:', error);
      toast({
        title: "Error",
        description: "Failed to delete scheme",
        variant: "destructive",
      });
    }
  };

  // Pagination logic
  const totalPages = Math.ceil(filteredSchemes.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentSchemes = filteredSchemes.slice(startIndex, endIndex);

  // Pagination handlers
  const handlePageChange = (page: number) => {
    if (page < 1 || page > totalPages || page === currentPage) return;

    setSearchParams(prev => {
      const newParams = new URLSearchParams(prev);
      newParams.set('page', page.toString());
      return newParams;
    });

    // Scroll to top smoothly
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const goToFirstPage = () => handlePageChange(1);
  const goToLastPage = () => handlePageChange(totalPages);
  const goToPreviousPage = () => handlePageChange(currentPage - 1);
  const goToNextPage = () => handlePageChange(currentPage + 1);

  // Reset to page 1 when filters change but preserve page on refresh
  useEffect(() => {
    if (currentPage > totalPages && totalPages > 0) {
      setSearchParams(prev => {
        const newParams = new URLSearchParams(prev);
        newParams.set('page', '1');
        return newParams;
      });
    }
  }, [filteredSchemes.length, currentPage, totalPages, setSearchParams]);

  // Reset to page 1 only when search or filters change (not on component mount)
  useEffect(() => {
    // Only reset if we're not on the initial load
    if (searchTerm || statusFilter !== 'all' || typeFilter !== 'all') {
      setSearchParams(prev => {
        const newParams = new URLSearchParams(prev);
        newParams.set('page', '1');
        return newParams;
      });
    }
  }, [searchTerm, statusFilter, typeFilter]);

  if (loading) {
    return <div className="flex items-center justify-center h-64">Loading schemes...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Schemes Management</h1>
        <Button onClick={() => navigate('/schemes/new')}>
          <Plus className="h-4 w-4 mr-2" />
          Add Scheme
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-2 md:gap-4">
        <Card
          className={`cursor-pointer hover:shadow-md transition-shadow ${statusFilter === 'all' && typeFilter === 'all' ? 'ring-2 ring-blue-500' : ''}`}
          onClick={() => {
            setStatusFilter('all');
            setTypeFilter('all');
          }}
        >
          <CardContent className="p-3 md:p-4">
            <div className="flex items-center gap-2 md:gap-3">
              <div className="p-1.5 md:p-2 bg-blue-100 rounded-lg">
                <TrendingUp className="h-4 w-4 md:h-5 md:w-5 text-blue-600" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-xs md:text-sm text-gray-600 truncate">Total Schemes</p>
                <p className="text-lg md:text-2xl font-bold">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card
          className={`cursor-pointer hover:shadow-md transition-shadow ${statusFilter === 'active' ? 'ring-2 ring-green-500' : ''}`}
          onClick={() => setStatusFilter('active')}
        >
          <CardContent className="p-3 md:p-4">
            <div className="flex items-center gap-2 md:gap-3">
              <div className="p-1.5 md:p-2 bg-green-100 rounded-lg">
                <Clock className="h-4 w-4 md:h-5 md:w-5 text-green-600" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-xs md:text-sm text-gray-600 truncate">Active</p>
                <p className="text-lg md:text-2xl font-bold">{stats.active}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card
          className={`cursor-pointer hover:shadow-md transition-shadow ${statusFilter === 'inactive' ? 'ring-2 ring-gray-500' : ''}`}
          onClick={() => setStatusFilter('inactive')}
        >
          <CardContent className="p-3 md:p-4">
            <div className="flex items-center gap-2 md:gap-3">
              <div className="p-1.5 md:p-2 bg-gray-100 rounded-lg">
                <Percent className="h-4 w-4 md:h-5 md:w-5 text-gray-600" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-xs md:text-sm text-gray-600 truncate">Inactive</p>
                <p className="text-lg md:text-2xl font-bold">{stats.inactive}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card
          className={`cursor-pointer hover:shadow-md transition-shadow ${typeFilter === 'sip' ? 'ring-2 ring-purple-500' : ''}`}
          onClick={() => setTypeFilter('sip')}
        >
          <CardContent className="p-3 md:p-4">
            <div className="flex items-center gap-2 md:gap-3">
              <div className="p-1.5 md:p-2 bg-purple-100 rounded-lg">
                <DollarSign className="h-4 w-4 md:h-5 md:w-5 text-purple-600" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-xs md:text-sm text-gray-600 truncate">SIP Supported</p>
                <p className="text-lg md:text-2xl font-bold">{stats.sipSupported}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col gap-4">

        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search by scheme name or code..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-full sm:w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
            </SelectContent>
          </Select>
          <Select value={typeFilter} onValueChange={setTypeFilter}>
            <SelectTrigger className="w-full sm:w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="simple">Simple Interest</SelectItem>
              <SelectItem value="compound">Compound Interest</SelectItem>
              <SelectItem value="sip">SIP Supported</SelectItem>
            </SelectContent>
          </Select>
          <div className="w-full lg:w-auto">

            <EnhancedDateFilter
              value={dateFilter}
              onChange={setDateFilter}
              customRange={customDateRange}
              onCustomRangeChange={setCustomDateRange}
            />
          </div>

        </div>
      </div>

      {/* Results Count */}
      <div className="flex items-center justify-between">
        <p className="text-sm text-gray-600">
          Showing {startIndex + 1}-{Math.min(endIndex, filteredSchemes.length)} of {filteredSchemes.length} schemes
        </p>
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600">Sort by:</span>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleSort('name')}
            className="text-sm"
          >
            Name {sortField === 'name' && (sortDirection === 'asc' ? '↑' : '↓')}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleSort('interest_rate')}
            className="text-sm"
          >
            Rate {sortField === 'interest_rate' && (sortDirection === 'asc' ? '↑' : '↓')}
          </Button>
        </div>
      </div>

      {/* Schemes Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {currentSchemes.length > 0 ? (
          currentSchemes.map((scheme) => (
            <Card key={scheme.id} className={`${!scheme.is_active ? 'opacity-60' : ''}`}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{scheme.name}</CardTitle>
                  <Switch
                    checked={scheme.is_active}
                    onCheckedChange={() => toggleSchemeStatus(scheme.id, scheme.is_active)}
                  />
                </div>
                <p className="text-sm text-gray-600">Code: {scheme.scheme_code}</p>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-gray-600">Interest Rate</p>
                    <p className="font-semibold">{scheme.interest_rate}% p.a.</p>
                  </div>
                  <div>
                    <p className="text-gray-600">Interest Type</p>
                    <p className="font-semibold capitalize">{scheme.interest_type}</p>
                  </div>
                  <div>
                    <p className="text-gray-600">Tenure</p>
                    <p className="font-semibold">{scheme.tenure_months} months</p>
                  </div>
                  <div>
                    <p className="text-gray-600">Payout</p>
                    <p className="font-semibold capitalize">{scheme.payout_type}</p>
                  </div>
                </div>

                <div className="border-t pt-3">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-600">Min Amount</p>
                      <p className="font-semibold">₹{scheme.min_amount.toLocaleString()}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Max Amount</p>
                      <p className="font-semibold">
                        {scheme.max_amount ? `₹${scheme.max_amount.toLocaleString()}` : 'No limit'}
                      </p>
                    </div>
                  </div>
                </div>

                {scheme.supports_sip && (
                  <div className="bg-blue-50 p-2 rounded text-sm">
                    <p className="text-blue-800 font-medium">SIP Supported</p>
                  </div>
                )}

                <div className="flex justify-between pt-3 gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => navigate(`/schemes/${scheme.id}`)}
                    className="flex-1"
                  >
                    <Eye className="h-4 w-4 mr-1" />
                    View
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => navigate(`/schemes/${scheme.id}/edit`)}
                    className="flex-1"
                  >
                    <Edit className="h-4 w-4 mr-1" />
                    Edit
                  </Button>
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button
                        size="sm"
                        variant="outline"
                        className="flex-1 text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4 mr-1" />
                        Delete
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                          This action cannot be undone. This will permanently delete the scheme
                          "{scheme.name}" and remove all associated data.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={() => deleteScheme(scheme.id)}
                          className="bg-red-600 hover:bg-red-700"
                        >
                          Delete
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </CardContent>
            </Card>
          ))
        ) : (
          <div className="col-span-full text-center py-8">
            <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No schemes found</h3>
            <p className="text-gray-500 mb-4">
              {searchTerm || statusFilter !== 'all' || typeFilter !== 'all'
                ? 'Try adjusting your search or filters'
                : 'Get started by adding your first scheme'
              }
            </p>
            {!searchTerm && statusFilter === 'all' && typeFilter === 'all' && (
              <Button onClick={() => navigate('/schemes/new')}>
                <Plus className="h-4 w-4 mr-2" />
                Add First Scheme
              </Button>
            )}
          </div>
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="mt-8 pt-6 border-t space-y-4">
          {/* Mobile Pagination */}
          <div className="flex sm:hidden items-center justify-between">
            <Button
              variant="outline"
              size="sm"
              onClick={goToPreviousPage}
              disabled={currentPage === 1}
              className="flex items-center gap-1 px-3"
            >
              <ChevronLeft className="h-4 w-4" />
              Prev
            </Button>

            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">
                {currentPage} / {totalPages}
              </span>
              <div className="flex items-center gap-1">
                {/* Show only current page and adjacent pages on mobile */}
                {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
                  let page: number;
                  if (totalPages <= 5) {
                    page = i + 1;
                  } else if (currentPage <= 3) {
                    page = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    page = totalPages - 4 + i;
                  } else {
                    page = currentPage - 2 + i;
                  }

                  return (
                    <Button
                      key={page}
                      variant={currentPage === page ? "default" : "outline"}
                      size="sm"
                      onClick={() => handlePageChange(page)}
                      className="w-8 h-8 p-0 text-xs"
                    >
                      {page}
                    </Button>
                  );
                })}
              </div>
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={goToNextPage}
              disabled={currentPage === totalPages}
              className="flex items-center gap-1 px-3"
            >
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>

          {/* Desktop Pagination */}
          <div className="hidden sm:flex items-center justify-between">
            <div className="text-sm text-gray-600">
              Page {currentPage} of {totalPages}
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={goToFirstPage}
                disabled={currentPage === 1}
              >
                First
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={goToPreviousPage}
                disabled={currentPage === 1}
                className="flex items-center gap-1"
              >
                <ChevronLeft className="h-4 w-4" />
                Previous
              </Button>

              {/* Page Numbers */}
              <div className="flex items-center gap-1">
                {Array.from({ length: totalPages }, (_, i) => i + 1)
                  .filter(page => {
                    // Show first page, last page, current page, and pages around current page
                    return page === 1 ||
                      page === totalPages ||
                      (page >= currentPage - 2 && page <= currentPage + 2);
                  })
                  .map((page, index, array) => {
                    // Add ellipsis if there's a gap
                    const prevPage = array[index - 1];
                    const showEllipsis = prevPage && page - prevPage > 1;

                    return (
                      <React.Fragment key={page}>
                        {showEllipsis && (
                          <span className="px-2 py-1 text-gray-400">...</span>
                        )}
                        <Button
                          variant={currentPage === page ? "default" : "outline"}
                          size="sm"
                          onClick={() => handlePageChange(page)}
                          className="w-10 h-10 p-0"
                        >
                          {page}
                        </Button>
                      </React.Fragment>
                    );
                  })}
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={goToNextPage}
                disabled={currentPage === totalPages}
                className="flex items-center gap-1"
              >
                Next
                <ChevronRight className="h-4 w-4" />
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={goToLastPage}
                disabled={currentPage === totalPages}
              >
                Last
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Schemes;
