import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Calendar } from 'lucide-react';

export interface DateRange {
  from: string;
  to: string;
}

interface DateFilterProps {
  value: string;
  onChange: (value: string) => void;
  customRange?: DateRange;
  onCustomRangeChange?: (range: DateRange) => void;
}

export const DateFilter: React.FC<DateFilterProps> = ({
  value,
  onChange,
  customRange,
  onCustomRangeChange
}) => {
  return (
    <div className="flex gap-2 items-center">
      <Select value={value} onValueChange={onChange}>
        <SelectTrigger className="w-48">
          <Calendar className="h-4 w-4 mr-2" />
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Time</SelectItem>
          <SelectItem value="today">Today</SelectItem>
          <SelectItem value="yesterday">Yesterday</SelectItem>
          <SelectItem value="last-week">Last Week</SelectItem>
          <SelectItem value="last-30">Last 30 Days</SelectItem>
          <SelectItem value="last-60">Last 60 Days</SelectItem>
          <SelectItem value="last-90">Last 90 Days</SelectItem>
          <SelectItem value="last-year">Last Year</SelectItem>
          <SelectItem value="custom">Custom Range</SelectItem>
        </SelectContent>
      </Select>

      {value === 'custom' && (
        <>
          <Input
            type="date"
            value={customRange?.from || ''}
            onChange={(e) => onCustomRangeChange?.({ ...customRange!, from: e.target.value })}
            className="w-36"
          />
          <Input
            type="date"
            value={customRange?.to || ''}
            onChange={(e) => onCustomRangeChange?.({ ...customRange!, to: e.target.value })}
            className="w-36"
          />
        </>
      )}
    </div>
  );
};

export const filterByDateRange = <T extends Record<string, any>>(
  data: T[],
  dateField: keyof T,
  filter: string,
  customRange?: DateRange
): T[] => {
  if (filter === 'all') return data;

  const today = new Date();
  let from: string, to: string;

  switch (filter) {
    case 'today':
      from = to = today.toISOString().split('T')[0];
      break;
    case 'yesterday':
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      from = to = yesterday.toISOString().split('T')[0];
      break;
    case 'last-week':
      const weekAgo = new Date(today);
      weekAgo.setDate(weekAgo.getDate() - 7);
      from = weekAgo.toISOString().split('T')[0];
      to = today.toISOString().split('T')[0];
      break;
    case 'last-30':
      const month30 = new Date(today);
      month30.setDate(month30.getDate() - 30);
      from = month30.toISOString().split('T')[0];
      to = today.toISOString().split('T')[0];
      break;
    case 'last-60':
      const month60 = new Date(today);
      month60.setDate(month60.getDate() - 60);
      from = month60.toISOString().split('T')[0];
      to = today.toISOString().split('T')[0];
      break;
    case 'last-90':
      const month90 = new Date(today);
      month90.setDate(month90.getDate() - 90);
      from = month90.toISOString().split('T')[0];
      to = today.toISOString().split('T')[0];
      break;
    case 'last-year':
      const yearAgo = new Date(today);
      yearAgo.setFullYear(yearAgo.getFullYear() - 1);
      from = yearAgo.toISOString().split('T')[0];
      to = today.toISOString().split('T')[0];
      break;
    case 'custom':
      if (!customRange?.from || !customRange?.to) return data;
      from = customRange.from;
      to = customRange.to;
      break;
    default:
      return data;
  }

  return data.filter(item => {
    const itemDate = new Date(item[dateField] as string).toISOString().split('T')[0];
    return itemDate >= from && itemDate <= to;
  });
};