import React, { useState, useEffect, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Search, Plus, Eye, Edit, Trash2, TrendingUp, DollarSign, Clock, AlertCircle, ArrowRightLeft, RotateCcw, ArrowUpDown, ChevronLeft, ChevronRight } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { EnhancedDate<PERSON>ilter, <PERSON><PERSON><PERSON><PERSON>, getDateRangeValues } from '@/components/ui/enhanced-date-filter';
import { debounce } from 'lodash';

interface Investment {
  id: string;
  client_id: string;
  scheme_name: string;
  scheme_code: string;
  amount: number;
  investment_date: string;
  maturity_date: string;
  maturity_amount: number;
  status: string;
  interest_rate: number;
  tenure_months: number;
  created_at: string;
  clients?: {
    first_name: string;
    last_name: string;
    mobile_number: string;
  } | null;
}

const Investments: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const [investments, setInvestments] = useState<Investment[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState(searchParams.get('search') || '');
  const [searchInput, setSearchInput] = useState(searchParams.get('search') || '');
  const [statusFilter, setStatusFilter] = useState(searchParams.get('status') || 'all');
  const [dateFilter, setDateFilter] = useState(searchParams.get('date') || 'all');
  const [customDateRange, setCustomDateRange] = useState<DateRange>();
  const [sortBy, setSortBy] = useState(searchParams.get('sortBy') || 'created_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>((searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc');

  // Pagination
  const [itemsPerPage, setItemsPerPage] = useState(parseInt(searchParams.get('limit') || '10'));
  const currentPage = parseInt(searchParams.get('page') || '1');
  const [totalCount, setTotalCount] = useState(0);
  const totalPages = Math.ceil(totalCount / itemsPerPage);

  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    matured: 0,
    totalValue: 0
  });

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce((value: string) => {
      setSearchTerm(value);
    }, 500),
    []
  );

  // Handle search input change
  useEffect(() => {
    debouncedSearch(searchInput);
    return () => {
      debouncedSearch.cancel();
    };
  }, [searchInput, debouncedSearch]);

  useEffect(() => {
    fetchInvestments();
    fetchStats();
  }, [currentPage, searchTerm, statusFilter, dateFilter, customDateRange, sortBy, sortOrder, itemsPerPage]);

  useEffect(() => {
    // Update URL with current filters
    const params = new URLSearchParams();
    if (searchTerm) params.set('search', searchTerm);
    if (statusFilter !== 'all') params.set('status', statusFilter);
    if (dateFilter !== 'all') params.set('date', dateFilter);
    if (sortBy !== 'created_at') params.set('sortBy', sortBy);
    if (sortOrder !== 'desc') params.set('sortOrder', sortOrder);
    if (currentPage !== 1) params.set('page', currentPage.toString());
    if (itemsPerPage !== 10) params.set('limit', itemsPerPage.toString());

    setSearchParams(params);
  }, [searchTerm, statusFilter, dateFilter, sortBy, sortOrder, currentPage, itemsPerPage, setSearchParams]);

  const fetchStats = async () => {
    try {
      const { data, error } = await supabase
        .from('investments')
        .select('status, amount')
        .eq('is_active', true);

      if (error) throw error;

      const totalCount = data?.length || 0;
      const activeCount = data?.filter(inv => inv.status === 'active').length || 0;
      const maturedCount = data?.filter(inv => inv.status === 'matured').length || 0;
      const totalValue = data?.filter(inv => inv.status === 'active' || inv.status === 'matured')
        .reduce((sum, inv) => sum + Number(inv.amount), 0) || 0;

      setStats({
        total: totalCount,
        active: activeCount,
        matured: maturedCount,
        totalValue
      });
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const fetchInvestments = async () => {
    setLoading(true);
    try {
      let query = supabase
        .from('investments')
        .select(`
          *,
          clients!investments_client_id_fkey (
            first_name,
            last_name,
            mobile_number
          )
        `, { count: 'exact' })
        .eq('is_active', true);

      // Apply search filter
      if (searchTerm) {
        query = query.or(`
          scheme_name.ilike.%${searchTerm}%,
          scheme_code.ilike.%${searchTerm}%,
          clients.first_name.ilike.%${searchTerm}%,
          clients.last_name.ilike.%${searchTerm}%,
          clients.mobile_number.ilike.%${searchTerm}%
        `);
      }

      // Apply status filter
      if (statusFilter !== 'all') {
        query = query.eq('status', statusFilter);
      }

      // Apply date filter
      const dateRange = getDateRangeValues(dateFilter, customDateRange);
      if (dateRange) {
        query = query.gte('investment_date', dateRange.from)
          .lte('investment_date', dateRange.to);
      }

      // Apply sorting
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });

      // Apply pagination
      const from = (currentPage - 1) * itemsPerPage;
      const to = from + itemsPerPage - 1;
      query = query.range(from, to);

      const { data, error, count } = await query;

      if (error) throw error;

      setInvestments(data || []);
      setTotalCount(count || 0);

    } catch (error) {
      console.error('Error fetching investments:', error);
      toast({
        title: "Error",
        description: "Failed to fetch investments",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('asc');
    }
    setSearchParams(prev => {
      const newParams = new URLSearchParams(prev);
      newParams.set('page', '1');
      return newParams;
    });
  };

  const handlePageChange = (page: number) => {
    if (page < 1 || page > totalPages || page === currentPage) return;
    setSearchParams(prev => {
      const newParams = new URLSearchParams(prev);
      newParams.set('page', page.toString());
      return newParams;
    });
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setSearchParams(prev => {
      const newParams = new URLSearchParams(prev);
      newParams.set('limit', newItemsPerPage.toString());
      newParams.set('page', '1');
      return newParams;
    });
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleDelete = async (investmentId: string) => {
    if (!confirm('Are you sure you want to delete this investment?')) return;

    try {
      const { error } = await supabase
        .from('investments')
        .update({ is_active: false })
        .eq('id', investmentId);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Investment deleted successfully",
      });

      fetchInvestments();
    } catch (error) {
      console.error('Error deleting investment:', error);
      toast({
        title: "Error",
        description: "Failed to delete investment",
        variant: "destructive",
      });
    }
  };

  const handleTransfer = (investment: Investment) => {
    // Navigate to transaction form with pre-filled values for transfer
    navigate('/transactions/new', {
      state: {
        prefilledData: {
          investment_id: investment.id,
          amount_type: 'maturity_payout',
          amount: investment.maturity_amount,
          transaction_date: new Date().toISOString().split('T')[0],
          remark: `Maturity transfer for ${investment.scheme_name}`,
          isTransfer: true
        }
      }
    });
  };

  const handleReinvestment = async (investment: Investment) => {
    try {
      // Navigate to create new investment first
      navigate('/investments/new', {
        state: {
          prefilledData: {
            client_id: investment.client_id,
            amount: investment.maturity_amount,
            reinvestment_source_id: investment.id,
            remark: `Reinvestment from ${investment.scheme_name}`,
            isReinvestment: true,
            originalInvestment: investment
          }
        }
      });

    } catch (error) {
      console.error('Error processing reinvestment:', error);
      toast({
        title: "Error",
        description: "Failed to process reinvestment",
        variant: "destructive",
      });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'matured': return 'bg-blue-100 text-blue-800';
      case 'transferred': return 'bg-purple-100 text-purple-800';
      case 'reinvested': return 'bg-orange-100 text-orange-800';
      case 'withdrawn': return 'bg-gray-100 text-gray-800';
      default: return 'bg-yellow-100 text-yellow-800';
    }
  };

  const isMaturitySoon = (maturityDate: string) => {
    const maturity = new Date(maturityDate);
    const today = new Date();
    const daysUntilMaturity = Math.ceil((maturity.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    return daysUntilMaturity <= 30 && daysUntilMaturity > 0;
  };

  const canShowMaturityActions = (investment: Investment) => {
    return investment.status === 'matured' &&
      !['transferred', 'reinvested'].includes(investment.status);
  };

  const SortableHeader = ({ column, children }: { column: string; children: React.ReactNode }) => (
    <TableHead
      className="cursor-pointer hover:bg-gray-50 select-none"
      onClick={() => handleSort(column)}
    >
      <div className="flex items-center gap-2">
        {children}
        <ArrowUpDown className="h-4 w-4" />
        {sortBy === column && (
          <span className="text-xs text-blue-600">
            {sortOrder === 'asc' ? '↑' : '↓'}
          </span>
        )}
      </div>
    </TableHead>
  );

  if (loading) {
    return <div className="flex items-center justify-center h-64">Loading investments...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Investments</h1>
        <Button onClick={() => navigate('/investments/new')}>
          <Plus className="h-4 w-4 mr-2" />
          Add Investment
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-2 md:gap-4">
        <Card
          className={`cursor-pointer hover:shadow-md transition-shadow ${statusFilter === 'all' ? 'ring-2 ring-blue-500' : ''}`}
          onClick={() => setStatusFilter('all')}
        >
          <CardContent className="p-3 md:p-4">
            <div className="flex items-center gap-2 md:gap-3">
              <div className="p-1.5 md:p-2 bg-blue-100 rounded-lg">
                <TrendingUp className="h-4 w-4 md:h-5 md:w-5 text-blue-600" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-xs md:text-sm text-gray-600 truncate">Total Investments</p>
                <p className="text-lg md:text-2xl font-bold">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card
          className={`cursor-pointer hover:shadow-md transition-shadow ${statusFilter === 'active' ? 'ring-2 ring-green-500' : ''}`}
          onClick={() => setStatusFilter('active')}
        >
          <CardContent className="p-3 md:p-4">
            <div className="flex items-center gap-2 md:gap-3">
              <div className="p-1.5 md:p-2 bg-green-100 rounded-lg">
                <Clock className="h-4 w-4 md:h-5 md:w-5 text-green-600" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-xs md:text-sm text-gray-600 truncate">Active</p>
                <p className="text-lg md:text-2xl font-bold">{stats.active}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card
          className={`cursor-pointer hover:shadow-md transition-shadow ${statusFilter === 'matured' ? 'ring-2 ring-purple-500' : ''}`}
          onClick={() => setStatusFilter('matured')}
        >
          <CardContent className="p-3 md:p-4">
            <div className="flex items-center gap-2 md:gap-3">
              <div className="p-1.5 md:p-2 bg-purple-100 rounded-lg">
                <AlertCircle className="h-4 w-4 md:h-5 md:w-5 text-purple-600" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-xs md:text-sm text-gray-600 truncate">Matured</p>
                <p className="text-lg md:text-2xl font-bold">{stats.matured}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-3 md:p-4">
            <div className="flex items-center gap-2 md:gap-3">
              <div className="p-1.5 md:p-2 bg-orange-100 rounded-lg">
                <DollarSign className="h-4 w-4 md:h-5 md:w-5 text-orange-600" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-xs md:text-sm text-gray-600 truncate">Total Active Investment Value</p>
                <p className="text-lg md:text-2xl font-bold">₹{stats.totalValue.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex flex-col gap-4">

        <div className="flex flex-col lg:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search by scheme, client name, or mobile..."
              value={searchInput}
              onChange={(e) => setSearchInput(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-full lg:w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="matured">Matured</SelectItem>
              <SelectItem value="transferred">Transferred</SelectItem>
              <SelectItem value="reinvested">Reinvested</SelectItem>
              <SelectItem value="withdrawn">Withdrawn</SelectItem>
            </SelectContent>
          </Select>
          <div className="w-full lg:w-auto">

            <EnhancedDateFilter
              value={dateFilter}
              onChange={setDateFilter}
              customRange={customDateRange}
              onCustomRangeChange={setCustomDateRange}
            />
          </div>
        </div>
      </div>

      {/* Investments List Table */}
      <Card>
        <CardHeader>
          <CardTitle>Investments List ({totalCount} total)</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          {investments.length > 0 ? (
            <>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-16 text-center">#</TableHead>
                      <SortableHeader column="clients.first_name">Client</SortableHeader>
                      <SortableHeader column="scheme_name">Scheme</SortableHeader>
                      <SortableHeader column="amount">Amount</SortableHeader>
                      <SortableHeader column="interest_rate">Interest Rate</SortableHeader>
                      <SortableHeader column="investment_date">Investment Date</SortableHeader>
                      <SortableHeader column="maturity_date">Maturity Date</SortableHeader>
                      <SortableHeader column="status">Status</SortableHeader>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {investments.map((investment, index) => (
                      <TableRow key={investment.id} className="hover:bg-gray-50">
                        <TableCell className="w-16 text-center text-sm text-gray-500 font-medium">
                          {(currentPage - 1) * itemsPerPage + index + 1}
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium text-gray-900">
                              {investment.clients?.first_name} {investment.clients?.last_name}
                            </div>
                            <div className="text-sm text-gray-500">{investment.clients?.mobile_number}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{investment.scheme_name}</div>
                            <div className="text-sm text-gray-500">{investment.scheme_code}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="font-medium text-green-600">
                            ₹{investment.amount.toLocaleString()}
                          </div>
                          {investment.status === 'matured' && (
                            <div className="text-sm text-blue-600">
                              Maturity: ₹{investment.maturity_amount.toLocaleString()}
                            </div>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="font-medium">{investment.interest_rate}%</div>
                          <div className="text-sm text-gray-500">{investment.tenure_months} months</div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {new Date(investment.investment_date).toISOString().split('T')[0]}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {new Date(investment.maturity_date).toISOString().split('T')[0]}
                          </div>
                          {isMaturitySoon(investment.maturity_date) && investment.status === 'active' && (
                            <Badge variant="outline" className="text-orange-600 border-orange-200 text-xs mt-1">
                              Maturing Soon
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(investment.status)}>
                            {investment.status}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-1">
                            {/* Always show View and Delete buttons */}
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => navigate(`/investments/${investment.id}`)}
                              className="h-8 w-8 p-0"
                              title="View Details"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>

                            {/* Show Edit for non-matured investments */}
                            {investment.status !== 'matured' && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => navigate(`/investments/${investment.id}/edit`)}
                                className="h-8 w-8 p-0"
                                title="Edit Investment"
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                            )}

                            {/* Show Transfer and Reinvest for matured investments */}
                            {canShowMaturityActions(investment) && (
                              <>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleTransfer(investment)}
                                  className="h-8 w-8 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                                  title="Transfer Maturity Amount"
                                >
                                  <ArrowRightLeft className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleReinvestment(investment)}
                                  className="h-8 w-8 p-0 text-green-600 hover:text-green-700 hover:bg-green-50"
                                  title="Reinvest"
                                >
                                  <RotateCcw className="h-4 w-4" />
                                </Button>
                              </>
                            )}

                            {/* Always show Delete button */}
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDelete(investment.id)}
                              className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                              title="Delete Investment"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Enhanced Responsive Pagination */}
              {totalPages > 1 && (
                <div className="p-4 border-t bg-gray-50">
                  {/* Mobile Pagination */}
                  <div className="flex flex-col space-y-4 sm:hidden">
                    <div className="text-sm text-gray-600 text-center">
                      Page {currentPage} of {totalPages} ({totalCount} total results)
                    </div>
                    <div className="flex justify-between items-center">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                        className="flex items-center gap-2 min-w-[80px]"
                      >
                        <span className="text-xs">‹</span>
                        Previous
                      </Button>

                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-500">Page</span>
                        <select
                          value={currentPage}
                          onChange={(e) => handlePageChange(Number(e.target.value))}
                          className="px-3 py-1 border border-gray-300 rounded-md text-sm bg-white min-w-[60px] text-center"
                        >
                          {Array.from({ length: totalPages }, (_, i) => (
                            <option key={i + 1} value={i + 1}>{i + 1}</option>
                          ))}
                        </select>
                        <span className="text-sm text-gray-500">of {totalPages}</span>
                      </div>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage === totalPages}
                        className="flex items-center gap-2 min-w-[80px]"
                      >
                        Next
                        <span className="text-xs">›</span>
                      </Button>
                    </div>
                  </div>

                  {/* Desktop Pagination */}
                  <div className="hidden sm:flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                    <div className="text-sm text-gray-600 order-2 lg:order-1">
                      Showing <span className="font-medium">{((currentPage - 1) * itemsPerPage) + 1}</span> to <span className="font-medium">{Math.min(currentPage * itemsPerPage, totalCount)}</span> of <span className="font-medium">{totalCount}</span> results
                    </div>

                    <div className="flex items-center justify-center lg:justify-end gap-2 order-1 lg:order-2">
                      {/* First Page Button */}
                      {currentPage > 3 && totalPages > 7 && (
                        <>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handlePageChange(1)}
                            className="w-10 h-10 p-0"
                          >
                            1
                          </Button>
                          {currentPage > 4 && (
                            <span className="px-2 text-gray-400">...</span>
                          )}
                        </>
                      )}

                      {/* Previous Button */}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                        className="flex items-center gap-1 px-3"
                      >
                        <span className="text-sm">‹</span>
                        <span className="hidden md:inline">Previous</span>
                      </Button>

                      {/* Page Numbers */}
                      <div className="flex items-center gap-1">
                        {Array.from({ length: Math.min(totalPages, 7) }, (_, i) => {
                          let page: number;

                          if (totalPages <= 7) {
                            page = i + 1;
                          } else if (currentPage <= 4) {
                            page = i + 1;
                          } else if (currentPage >= totalPages - 3) {
                            page = totalPages - 6 + i;
                          } else {
                            page = currentPage - 3 + i;
                          }

                          // Don't show if it's already covered by first/last
                          if ((currentPage > 3 && totalPages > 7 && page === 1) ||
                            (currentPage < totalPages - 2 && totalPages > 7 && page === totalPages)) {
                            return null;
                          }

                          return (
                            <Button
                              key={page}
                              variant={currentPage === page ? "default" : "outline"}
                              size="sm"
                              onClick={() => handlePageChange(page)}
                              className={`w-10 h-10 p-0 ${currentPage === page
                                ? 'bg-blue-600 text-white hover:bg-blue-700'
                                : 'hover:bg-gray-100'
                                }`}
                            >
                              {page}
                            </Button>
                          );
                        })}
                      </div>

                      {/* Next Button */}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage === totalPages}
                        className="flex items-center gap-1 px-3"
                      >
                        <span className="hidden md:inline">Next</span>
                        <span className="text-sm">›</span>
                      </Button>

                      {/* Last Page Button */}
                      {currentPage < totalPages - 2 && totalPages > 7 && (
                        <>
                          {currentPage < totalPages - 3 && (
                            <span className="px-2 text-gray-400">...</span>
                          )}
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handlePageChange(totalPages)}
                            className="w-10 h-10 p-0"
                          >
                            {totalPages}
                          </Button>
                        </>
                      )}
                    </div>
                  </div>

                  {/* Items per page selector for larger screens */}
                  <div className="hidden xl:flex items-center justify-center mt-4 pt-4 border-t border-gray-200">
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <span>Show</span>
                      <select
                        value={itemsPerPage}
                        onChange={(e) => handleItemsPerPageChange(Number(e.target.value))}
                        className="px-2 py-1 border border-gray-300 rounded text-sm bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="10">10</option>
                        <option value="25">25</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                      </select>
                      <span>entries per page</span>
                    </div>
                  </div>
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-12">
              <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No investments found</h3>
              <p className="text-gray-500 mb-4">
                {searchTerm || statusFilter !== 'all' || dateFilter !== 'all'
                  ? 'Try adjusting your search or filters'
                  : 'Get started by adding your first investment'
                }
              </p>
              {!searchTerm && statusFilter === 'all' && dateFilter === 'all' && (
                <Button onClick={() => navigate('/investments/new')}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add First Investment
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default Investments;
