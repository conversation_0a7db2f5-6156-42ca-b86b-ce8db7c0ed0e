
import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { EnhancedDatePicker } from '@/components/ui/enhanced-date-picker';
import { Textarea } from '@/components/ui/textarea';
import { ArrowLeft } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import ClientSelector from '@/components/investment/ClientSelector';
import SchemeSelector from '@/components/investment/SchemeSelector';
import NomineeSelector from '@/components/investment/NomineeSelector';

interface Client {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
}

interface Scheme {
  id: string;
  name: string;
  scheme_code: string;
  interest_rate: number;
  interest_type: string;
  tenure_months: number;
  min_amount: number;
  max_amount: number;
  lock_in_period_months: number;
  commission_percentage: number;
  compounding_frequency: string;
  payout_type: string;
  supports_sip: boolean;
}

interface Nominee {
  id: string;
  name: string;
  relation: string;
}

const InvestmentEdit: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [tdsPercentage, setTdsPercentage] = useState(10);

  // Form state
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [selectedSecondaryApplicant, setSelectedSecondaryApplicant] = useState<Client | null>(null);
  const [selectedScheme, setSelectedScheme] = useState<Scheme | null>(null);
  const [selectedNominee, setSelectedNominee] = useState<Nominee | null>(null);
  const [amount, setAmount] = useState<string>('');
  const [investmentDate, setInvestmentDate] = useState<Date>();
  const [startDate, setStartDate] = useState<Date>();
  const [maturityDate, setMaturityDate] = useState<Date>();
  const [maturityAmount, setMaturityAmount] = useState<number>(0);
  const [commission, setCommission] = useState<number>(0);
  const [tdsAmount, setTdsAmount] = useState<number>(0);
  const [actualProfit, setActualProfit] = useState<number>(0);
  const [remark, setRemark] = useState('');

  useEffect(() => {
    if (id) {
      fetchInvestmentData();
    }
    fetchTdsPercentage();
  }, [id]);

  useEffect(() => {
    if (selectedScheme && amount && investmentDate) {
      calculateMaturityDetails();
    }
  }, [selectedScheme, amount, investmentDate, tdsPercentage]);

  const fetchTdsPercentage = async () => {
    try {
      const { data, error } = await supabase
        .from('notification_settings')
        .select('tds_percentage')
        .maybeSingle();

      if (error) {
        console.error('Error fetching TDS percentage:', error);
        return;
      }
      if (data?.tds_percentage) {
        setTdsPercentage(data.tds_percentage);
      }
    } catch (error) {
      console.error('Error fetching TDS percentage:', error);
    }
  };

  const calculateMaturityDetails = () => {
    if (!selectedScheme || !amount || !investmentDate) return;

    const principal = parseFloat(amount);
    const rate = selectedScheme.interest_rate;
    const tenureMonths = selectedScheme.tenure_months;

    // Maturity Date
    const maturity = new Date(investmentDate);
    maturity.setMonth(maturity.getMonth() + tenureMonths);
    setMaturityDate(maturity);

    // Monthly interest rate
    const monthlyRate = rate / 100 / 12;

    // Maturity Amount
    const maturityAmt = principal + (principal * monthlyRate * tenureMonths);
    setMaturityAmount(maturityAmt);

    // Commission
    const commissionAmt = (principal * selectedScheme.commission_percentage) / 100;
    setCommission(commissionAmt);

    // TDS on commission
    const tds = (commissionAmt * tdsPercentage) / 100;
    setTdsAmount(tds);

    // Actual Profit = Commission - TDS
    const actualProfitAmt = commissionAmt - tds;
    setActualProfit(actualProfitAmt);
  };

  const fetchInvestmentData = async () => {
    try {
      const { data, error } = await supabase
        .from('investments')
        .select(`
          *,
          client:clients!client_id (id, first_name, last_name, email),
          secondary_applicant:clients!second_applicant_id (id, first_name, last_name, email),
          nominee:nominees!nominee_id (id, name, relation)
        `)
        .eq('id', id)
        .single();

      if (error) throw error;

      // Set form values
      setSelectedClient(data.client);
      setSelectedSecondaryApplicant(data.secondary_applicant);
      setSelectedNominee(data.nominee);
      setAmount(data.amount.toString());
      setInvestmentDate(new Date(data.investment_date));
      setStartDate(new Date(data.start_date));
      setRemark(data.remark || '');

      // Create scheme object from stored data
      const scheme: Scheme = {
        id: data.scheme_id,
        name: data.scheme_name,
        scheme_code: data.scheme_code,
        interest_rate: data.interest_rate,
        interest_type: data.interest_type,
        tenure_months: data.tenure_months,
        min_amount: data.min_amount,
        max_amount: data.max_amount,
        lock_in_period_months: data.lock_in_period_months,
        commission_percentage: data.commission_percentage,
        compounding_frequency: data.compounding_frequency,
        payout_type: data.payout_status,
        supports_sip: data.supports_sip
      };
      setSelectedScheme(scheme);

    } catch (error) {
      console.error('Error fetching investment:', error);
      toast({
        title: "Error",
        description: "Failed to fetch investment details",
        variant: "destructive",
      });
    } finally {
      setInitialLoading(false);
    }
  };

  // Format dates to avoid timezone issues
  const formatDateForDB = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedClient || !selectedScheme || !amount || !investmentDate || !startDate) {
      toast({
        title: "Error",
        description: "Please fill all required fields",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      const updateData = {
        client_id: selectedClient.id,
        second_applicant_id: selectedSecondaryApplicant?.id || null,
        scheme_id: selectedScheme.id,
        nominee_id: selectedNominee?.id || null,
        amount: parseFloat(amount),
        investment_date: formatDateForDB(investmentDate),
        start_date: formatDateForDB(startDate),
        maturity_date: maturityDate ? formatDateForDB(maturityDate) : null,
        maturity_amount: maturityAmount,
        remark: remark || null,
        actual_profit: actualProfit,
        tds_amount: tdsAmount,
        // Update scheme details
        scheme_name: selectedScheme.name,
        scheme_code: selectedScheme.scheme_code,
        interest_rate: selectedScheme.interest_rate,
        interest_type: selectedScheme.interest_type,
        tenure_months: selectedScheme.tenure_months,
        min_amount: selectedScheme.min_amount,
        max_amount: selectedScheme.max_amount,
        lock_in_period_months: selectedScheme.lock_in_period_months,
        commission_percentage: selectedScheme.commission_percentage,
        compounding_frequency: selectedScheme.compounding_frequency,
        payout_status: selectedScheme.payout_type,
        supports_sip: selectedScheme.supports_sip,
        updated_at: new Date().toISOString()
      };

      const { error } = await supabase
        .from('investments')
        .update(updateData)
        .eq('id', id);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Investment updated successfully",
      });

      navigate(`/investments/${id}`);
    } catch (error) {
      console.error('Error updating investment:', error);
      toast({
        title: "Error",
        description: "Failed to update investment",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  if (initialLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading investment...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4 sm:p-6 lg:p-8">
      <div className=" space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={() => navigate(`/investments/${id}`)}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Edit Investment</h1>
        </div>

        <form onSubmit={handleSubmit}>
          <Card className="shadow-lg">
            <CardHeader className="bg-white border-b">
              <CardTitle className="text-xl text-gray-800">Investment Details</CardTitle>
            </CardHeader>
            <CardContent className="p-6 space-y-6">
              {/* Client Selection */}
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">Primary Client *</Label>
                <ClientSelector
                  selectedClient={selectedClient}
                  onClientSelect={setSelectedClient}
                  required
                />
              </div>

              {/* Secondary Applicant */}
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">Secondary Applicant (Optional)</Label>
                <ClientSelector
                  selectedClient={selectedSecondaryApplicant}
                  onClientSelect={setSelectedSecondaryApplicant}
                  required={false}
                />
              </div>

              {/* Scheme Selection */}
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">Investment Scheme *</Label>
                <SchemeSelector
                  selectedScheme={selectedScheme}
                  onSchemeSelect={setSelectedScheme}
                />
              </div>

              {/* Show scheme details if selected */}
              {selectedScheme && (
                <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                  <h4 className="font-medium text-blue-800 mb-3">Scheme Details</h4>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                    <div><span className="font-medium text-gray-700">Interest Rate:</span> <span className="text-gray-600">{selectedScheme.interest_rate}%</span></div>
                    <div><span className="font-medium text-gray-700">Tenure:</span> <span className="text-gray-600">{selectedScheme.tenure_months} months</span></div>
                    <div><span className="font-medium text-gray-700">Interest Type:</span> <span className="text-gray-600">{selectedScheme.interest_type}</span></div>
                    <div><span className="font-medium text-gray-700">Min Amount:</span> <span className="text-gray-600">₹{selectedScheme.min_amount?.toLocaleString()}</span></div>
                    <div><span className="font-medium text-gray-700">Max Amount:</span> <span className="text-gray-600">₹{selectedScheme.max_amount?.toLocaleString()}</span></div>
                    <div><span className="font-medium text-gray-700">Commission:</span> <span className="text-gray-600">{selectedScheme.commission_percentage}%</span></div>
                  </div>
                </div>
              )}

              {/* Nominee Selection */}
              {selectedClient && (
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700">Nominee (Optional)</Label>
                  <NomineeSelector
                    clientId={selectedClient.id}
                    selectedNominee={selectedNominee}
                    onNomineeSelect={setSelectedNominee}
                  />
                </div>
              )}

              {/* Amount */}
              <div className="space-y-2">
                <Label htmlFor="amount" className="text-sm font-medium text-gray-700">Investment Amount *</Label>
                <Input
                  id="amount"
                  type="number"
                  placeholder="Enter investment amount"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  className="h-10"
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Investment Date */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700">Investment Date *</Label>
                  <EnhancedDatePicker
                    date={investmentDate}
                    onSelect={setInvestmentDate}
                    placeholder="Select investment date"
                  />
                </div>

                {/* Start Date */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700">Start Date *</Label>
                  <EnhancedDatePicker
                    date={startDate}
                    onSelect={setStartDate}
                    placeholder="Select start date"
                  />
                </div>
              </div>

              {/* Calculated Fields */}
              {maturityDate && (
                <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                  <h4 className="font-medium text-green-800 mb-3">Calculated Details</h4>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                    <div><span className="font-medium text-gray-700">Maturity Date:</span> <span className="text-gray-600">{maturityDate.toLocaleDateString()}</span></div>
                    <div><span className="font-medium text-gray-700">Maturity Amount:</span> <span className="text-gray-600">₹{maturityAmount.toLocaleString()}</span></div>
                    <div><span className="font-medium text-gray-700">Commission:</span> <span className="text-gray-600">₹{commission.toLocaleString()}</span></div>
                    <div><span className="font-medium text-gray-700">TDS Amount:</span> <span className="text-gray-600">₹{tdsAmount.toLocaleString()}</span></div>
                    <div><span className="font-medium text-gray-700">Actual Profit:</span> <span className="text-gray-600">₹{actualProfit.toLocaleString()}</span></div>
                  </div>
                </div>
              )}

              {/* Remark */}
              <div className="space-y-2">
                <Label htmlFor="remark" className="text-sm font-medium text-gray-700">Remark (Optional)</Label>
                <Textarea
                  id="remark"
                  placeholder="Enter any remarks"
                  value={remark}
                  onChange={(e) => setRemark(e.target.value)}
                  rows={3}
                />
              </div>

              <div className="flex flex-col sm:flex-row justify-end gap-4 pt-6 border-t">
                <Button type="button" variant="outline" onClick={() => navigate(`/investments/${id}`)} className="w-full sm:w-auto">
                  Cancel
                </Button>
                <Button type="submit" disabled={loading} className="w-full sm:w-auto">
                  {loading ? 'Updating...' : 'Update Investment'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </form>
      </div>
    </div>
  );
};

export default InvestmentEdit;
