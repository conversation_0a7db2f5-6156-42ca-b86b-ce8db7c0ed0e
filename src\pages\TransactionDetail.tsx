import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft, FileDown, Edit } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';

interface Client {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  mobile_number: string;
  address: string;
  city: string;
  state: string;
  pincode: string;
}

interface Investment {
  id: string;
  scheme_name: string;
  scheme_code: string;
  amount: number;
  client: Client;
}

interface TransactionDetail {
  id: string;
  amount: number;
  amount_type: string;
  transaction_date: string;
  reference_number: string;
  remark: string;
  created_at: string;
  investment: Investment;
}

const TransactionDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [transaction, setTransaction] = useState<TransactionDetail | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (id) {
      fetchTransactionDetail();
    }
  }, [id]);

  const fetchTransactionDetail = async () => {
    try {
      const { data, error } = await supabase
        .from('transactions')
        .select(`
          *,
          investment:investment_id(
            id, scheme_name, scheme_code, amount,
            client:clients!investments_client_id_fkey(
              id, first_name, last_name, email, mobile_number,
              address, city, state, pincode
            )
          )
        `)
        .eq('id', id)
        .single();

      if (error) throw error;

      if (
        data &&
        data.investment &&
        data.investment.client &&
        typeof data.investment.client === 'object' &&
        data.investment.client.id
      ) {
        const transactionData: TransactionDetail = {
          ...data,
          investment: {
            ...data.investment,
            client: data.investment.client
          }
        };
        setTransaction(transactionData);
      } else {
        throw new Error('Invalid transaction data structure');
      }
    } catch (error) {
      console.error('Error fetching transaction:', error);
      toast({
        title: "Error",
        description: "Failed to fetch transaction details",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const generateReceipt = () => {
    if (!transaction) return;

    const receiptWindow = window.open('', '_blank');
    if (!receiptWindow) {
      toast({
        title: "Error",
        description: "Could not open receipt window. Please check your browser settings.",
        variant: "destructive",
      });
      return;
    }

    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Transaction Receipt</title>
        <style>
          body { font-family: 'Segoe UI', Arial, sans-serif; margin: 40px; color: #222; background: #fff; }
          .header {
            display: flex;
            align-items: center;
            gap: 18px;
            border-bottom: 2.5px solid #2563eb;
            padding-bottom: 18px;
            margin-bottom: 32px;
          }
          .logo-section img {
            max-height: 44px;
            object-fit: contain;
          }
          .header-title {
            display: flex;
            flex-direction: column;
            gap: 2px;
          }
          .header-title h1 {
            font-size: 1.7rem;
            font-weight: 700;
            margin: 0;
            color: #1d3557;
            letter-spacing: -1px;
          }
          .header-title .subtitle {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2563eb;
            margin: 0;
            letter-spacing: 0.5px;
          }
          .section { margin-bottom: 30px; }
          .section h2 { color: #2563eb; border-bottom: 1px solid #e5e7eb; padding-bottom: 8px; font-size: 1.08rem; margin-bottom: 12px; }
          .detail-item { display: flex; justify-content: space-between; padding: 7px 0; border-bottom: 1px dotted #e5e7eb; font-size: 13px; }
          .detail-label { font-weight: 600; color: #374151; }
          .amount { font-size: 26px; font-weight: bold; color: #059669; text-align: center; margin: 22px 0 18px 0; }
          .footer { margin-top: 40px; text-align: center; color: #888; font-size: 11px; border-top: 1px solid #e5e7eb; padding-top: 12px; }
        </style>
      </head>
      <body>
        <div class="header">
          <div class="logo-section">
            <img src="/lovable-uploads/ab651bb9-5504-40da-ab30-1bd5fd7fdc5a.png" alt="InvestPro Logo" />
          </div>
          <div class="header-title">
            <h1>InvestPro</h1>
            <span class="subtitle">Transaction Receipt</span>
          </div>
        </div>
        <div style="text-align:right; color:#888; font-size:12px; margin-top:-24px; margin-bottom:18px;">Reference: <b>${transaction.reference_number}</b><br>Date: ${new Date(transaction.transaction_date).toLocaleDateString()}</div>
        <div class="section">
          <h2>Transaction Details</h2>
          <div class="detail-item">
            <span class="detail-label">Transaction ID:</span>
            <span>${transaction.id}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">Amount Type:</span>
            <span>${transaction.amount_type.charAt(0).toUpperCase() + transaction.amount_type.slice(1)}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">Transaction Date:</span>
            <span>${new Date(transaction.transaction_date).toLocaleDateString()}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">Reference Number:</span>
            <span>${transaction.reference_number}</span>
          </div>
          ${transaction.remark ? `
          <div class="detail-item">
            <span class="detail-label">Remark:</span>
            <span>${transaction.remark}</span>
          </div>` : ''}
        </div>
        <div class="amount">₹${transaction.amount.toLocaleString()}</div>
        <div class="section">
          <h2>Client Information</h2>
          <div class="detail-item">
            <span class="detail-label">Name:</span>
            <span>${transaction.investment.client.first_name} ${transaction.investment.client.last_name}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">Mobile:</span>
            <span>${transaction.investment.client.mobile_number}</span>
          </div>
          ${transaction.investment.client.email ? `
          <div class="detail-item">
            <span class="detail-label">Email:</span>
            <span>${transaction.investment.client.email}</span>
          </div>` : ''}
        </div>
        <div class="section">
          <h2>Investment Information</h2>
          <div class="detail-item">
            <span class="detail-label">Scheme:</span>
            <span>${transaction.investment.scheme_name}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">Scheme Code:</span>
            <span>${transaction.investment.scheme_code}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">Investment Amount:</span>
            <span>₹${transaction.investment.amount.toLocaleString()}</span>
          </div>
        </div>
        <div class="footer">
          Generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}<br>
          © ${new Date().getFullYear()} InvestPro. All rights reserved.
        </div>
      </body>
      </html>
    `;

    receiptWindow.document.write(htmlContent);
    receiptWindow.document.close();
    receiptWindow.focus();

    setTimeout(() => {
      receiptWindow.print();
      receiptWindow.close();
    }, 250);

    toast({
      title: "Receipt Generated",
      description: "Transaction receipt has been generated. Use your browser's print dialog to save as PDF.",
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading transaction details...</p>
        </div>
      </div>
    );
  }

  if (!transaction) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Transaction not found</h2>
          <Button onClick={() => navigate('/transactions')}>
            Back to Transactions
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4 sm:p-6 lg:p-8 ">
      <div className=" space-y-6">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <div className="flex items-center gap-4">
            <Button variant="ghost" onClick={() => navigate('/transactions')}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Transaction Details</h1>
          </div>
          <div className="flex gap-2 w-full sm:w-auto">
            <Button onClick={generateReceipt} className="flex-1 sm:flex-none">
              <FileDown className="h-4 w-4 mr-2" />
              Generate Receipt
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Transaction Information */}
          <Card className="shadow-sm">
            <CardHeader className="bg-white border-b">
              <CardTitle className="text-lg text-gray-800">Transaction Information</CardTitle>
            </CardHeader>
            <CardContent className="p-6 space-y-4">
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <p className="text-3xl font-bold text-green-600">₹{transaction.amount.toLocaleString()}</p>
                <p className="text-sm text-gray-600 capitalize">{transaction.amount_type.replace('_', ' ')}</p>
              </div>

              <div className="space-y-2">
                <span className="text-sm font-medium text-gray-700">Reference Number:</span>
                <p className="text-gray-900 font-mono">{transaction.reference_number}</p>
              </div>

              <div className="space-y-2">
                <span className="text-sm font-medium text-gray-700">Transaction Date:</span>
                <p className="text-gray-900">{new Date(transaction.transaction_date).toLocaleDateString()}</p>
              </div>

              <div className="space-y-2">
                <span className="text-sm font-medium text-gray-700">Created On:</span>
                <p className="text-gray-900">{new Date(transaction.created_at).toLocaleDateString()}</p>
              </div>

              {transaction.remark && (
                <div className="space-y-2">
                  <span className="text-sm font-medium text-gray-700">Remark:</span>
                  <p className="text-gray-900">{transaction.remark}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Client Information */}
          <Card className="shadow-sm">
            <CardHeader className="bg-white border-b">
              <CardTitle className="text-lg text-gray-800">Client Information</CardTitle>
            </CardHeader>
            <CardContent className="p-6 space-y-4">
              <div className="space-y-2">
                <span className="text-sm font-medium text-gray-700">Name:</span>
                <p className="text-gray-900">{transaction.investment.client.first_name} {transaction.investment.client.last_name}</p>
              </div>

              <div className="space-y-2">
                <span className="text-sm font-medium text-gray-700">Mobile:</span>
                <p className="text-gray-900">{transaction.investment.client.mobile_number}</p>
              </div>

              {transaction.investment.client.email && (
                <div className="space-y-2">
                  <span className="text-sm font-medium text-gray-700">Email:</span>
                  <p className="text-gray-900">{transaction.investment.client.email}</p>
                </div>
              )}

              <div className="space-y-2">
                <span className="text-sm font-medium text-gray-700">Address:</span>
                <p className="text-gray-900">
                  {transaction.investment.client.address}, {transaction.investment.client.city},
                  {transaction.investment.client.state} - {transaction.investment.client.pincode}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Investment Information */}
          <Card className="shadow-sm lg:col-span-2">
            <CardHeader className="bg-white border-b">
              <CardTitle className="text-lg text-gray-800">Related Investment</CardTitle>
            </CardHeader>
            <CardContent className="p-6 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <span className="text-sm font-medium text-gray-700">Scheme Name:</span>
                  <p className="text-gray-900">{transaction.investment.scheme_name}</p>
                </div>

                <div className="space-y-2">
                  <span className="text-sm font-medium text-gray-700">Scheme Code:</span>
                  <p className="text-gray-900">{transaction.investment.scheme_code}</p>
                </div>

                <div className="space-y-2">
                  <span className="text-sm font-medium text-gray-700">Investment Amount:</span>
                  <p className="text-gray-900 font-semibold">₹{transaction.investment.amount.toLocaleString()}</p>
                </div>
              </div>

              <div className="mt-4">
                <Button
                  variant="outline"
                  onClick={() => navigate(`/investments/${transaction.investment.id}`)}
                >
                  View Investment Details
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default TransactionDetail;
